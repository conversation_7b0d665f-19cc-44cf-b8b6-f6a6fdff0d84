import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { UserModel } from '../models/User';
import { UserActivityModel } from '../models/UserActivity';
import { UserSessionModel } from '../models/UserSession';
import { 
  User, 
  UserRole, 
  UserStatus, 
  UserActivityType,
  RiskLevel,
  UserPreferences,
  Address 
} from '@marcat/shared-types';
import { logger } from '../utils/logger';
import { ValidationError, NotFoundError, AuthorizationError } from '../middleware/errorHandler';
import { setCache, getCache, deleteCache } from '../config/redis';

export class UserService {
  private static readonly SALT_ROUNDS = 12;

  // Get user by ID
  static async getUserById(userId: string, requestingUserId?: string): Promise<User> {
    try {
      // Check cache first
      let user = await getCache(`user:${userId}`);
      if (!user) {
        user = await UserModel.findById(userId);
        if (user) {
          await setCache(`user:${userId}`, user, 3600); // 1 hour
        }
      }

      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Check if requesting user has permission to view this user
      if (requestingUserId && requestingUserId !== userId) {
        const requestingUser = await this.getUserById(requestingUserId);
        if (requestingUser.role !== UserRole.ADMIN && requestingUser.role !== UserRole.SUPER_ADMIN) {
          // Return limited public profile for non-admin users
          return this.getPublicProfile(user);
        }
      }

      return user;
    } catch (error) {
      logger.error('Error getting user by ID:', error);
      throw error;
    }
  }

  // Get user profile (public information only)
  static async getUserProfile(userId: string): Promise<Partial<User>> {
    try {
      const user = await this.getUserById(userId);
      return this.getPublicProfile(user);
    } catch (error) {
      logger.error('Error getting user profile:', error);
      throw error;
    }
  }

  // Update user profile
  static async updateProfile(
    userId: string,
    updateData: {
      firstName?: string;
      lastName?: string;
      phoneNumber?: string;
      dateOfBirth?: Date;
      bio?: string;
      preferences?: UserPreferences;
      address?: Address;
    },
    ipAddress: string,
    userAgent?: string
  ): Promise<User> {
    try {
      // Get current user
      const currentUser = await this.getUserById(userId);
      if (!currentUser) {
        throw new NotFoundError('User not found');
      }

      // Update user
      const updatedUser = await UserModel.update(userId, {
        first_name: updateData.firstName,
        last_name: updateData.lastName,
        phone_number: updateData.phoneNumber,
        date_of_birth: updateData.dateOfBirth,
        bio: updateData.bio,
        preferences: updateData.preferences,
        address: updateData.address,
      });

      if (!updatedUser) {
        throw new NotFoundError('User not found');
      }

      // Update cache
      await setCache(`user:${userId}`, updatedUser, 3600);

      // Log activity
      await UserActivityModel.create({
        userId,
        activityType: UserActivityType.PROFILE_UPDATE,
        description: 'User profile updated',
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.LOW,
        metadata: {
          updatedFields: Object.keys(updateData),
        },
      });

      logger.info('User profile updated', { userId, updatedFields: Object.keys(updateData) });

      return updatedUser;
    } catch (error) {
      logger.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Change password
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    ipAddress: string,
    userAgent?: string
  ): Promise<{ message: string }> {
    try {
      // Get current user
      const user = await this.getUserById(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
      if (!isCurrentPasswordValid) {
        await UserActivityModel.create({
          userId,
          activityType: UserActivityType.PASSWORD_CHANGE_FAILED,
          description: 'Password change failed - invalid current password',
          ipAddress,
          userAgent,
          riskLevel: RiskLevel.MEDIUM,
        });

        throw new ValidationError('Current password is incorrect');
      }

      // Hash new password
      const newPasswordHash = await bcrypt.hash(newPassword, this.SALT_ROUNDS);

      // Update password
      await UserModel.updatePassword(userId, newPasswordHash);

      // Revoke all sessions except current one (force re-login on other devices)
      await UserSessionModel.revokeAllForUser(userId, 'password_change');

      // Remove user from cache
      await deleteCache(`user:${userId}`);

      // Log activity
      await UserActivityModel.create({
        userId,
        activityType: UserActivityType.PASSWORD_CHANGE,
        description: 'Password changed successfully',
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.LOW,
      });

      logger.info('Password changed successfully', { userId });

      return { message: 'Password changed successfully. You have been logged out from all other devices.' };
    } catch (error) {
      logger.error('Error changing password:', error);
      throw error;
    }
  }

  // Update user avatar
  static async updateAvatar(
    userId: string,
    avatarUrl: string,
    ipAddress: string,
    userAgent?: string
  ): Promise<User> {
    try {
      const updatedUser = await UserModel.update(userId, { avatar_url: avatarUrl });
      if (!updatedUser) {
        throw new NotFoundError('User not found');
      }

      // Update cache
      await setCache(`user:${userId}`, updatedUser, 3600);

      // Log activity
      await UserActivityModel.create({
        userId,
        activityType: UserActivityType.PROFILE_UPDATE,
        description: 'User avatar updated',
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.LOW,
      });

      logger.info('User avatar updated', { userId });

      return updatedUser;
    } catch (error) {
      logger.error('Error updating user avatar:', error);
      throw error;
    }
  }

  // Get user sessions
  static async getUserSessions(userId: string): Promise<any[]> {
    try {
      const sessions = await UserSessionModel.findByUserId(userId, true);
      
      // Remove sensitive information
      return sessions.map(session => ({
        id: session.id,
        deviceType: session.deviceType,
        deviceName: session.deviceName,
        ipAddress: session.ipAddress,
        country: session.country,
        city: session.city,
        lastActivityAt: session.lastActivityAt,
        createdAt: session.createdAt,
      }));
    } catch (error) {
      logger.error('Error getting user sessions:', error);
      throw error;
    }
  }

  // Revoke user session
  static async revokeSession(
    userId: string,
    sessionId: string,
    ipAddress: string,
    userAgent?: string
  ): Promise<{ message: string }> {
    try {
      // Verify session belongs to user
      const sessions = await UserSessionModel.findByUserId(userId, true);
      const session = sessions.find(s => s.id === sessionId);
      
      if (!session) {
        throw new NotFoundError('Session not found');
      }

      // Revoke session
      await UserSessionModel.revoke(sessionId, 'user_revoked');

      // Log activity
      await UserActivityModel.create({
        userId,
        activityType: UserActivityType.SESSION_REVOKED,
        description: 'User revoked a session',
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.LOW,
        metadata: { sessionId },
      });

      logger.info('User session revoked', { userId, sessionId });

      return { message: 'Session revoked successfully' };
    } catch (error) {
      logger.error('Error revoking user session:', error);
      throw error;
    }
  }

  // Get user activity history
  static async getUserActivity(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      activityType?: UserActivityType;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<{ activities: any[]; total: number; page: number; limit: number }> {
    try {
      const result = await UserActivityModel.findByUserId(userId, options);
      
      // Remove sensitive information
      const activities = result.activities.map(activity => ({
        id: activity.id,
        activityType: activity.activityType,
        description: activity.description,
        ipAddress: activity.ipAddress,
        country: activity.country,
        city: activity.city,
        riskLevel: activity.riskLevel,
        createdAt: activity.createdAt,
      }));

      return {
        activities,
        total: result.total,
        page: result.page,
        limit: result.limit,
      };
    } catch (error) {
      logger.error('Error getting user activity:', error);
      throw error;
    }
  }

  // Get user statistics
  static async getUserStats(userId: string): Promise<{
    profile: {
      joinDate: Date;
      lastLogin: Date | null;
      emailVerified: boolean;
      twoFactorEnabled: boolean;
    };
    activity: {
      totalActivities: number;
      activitiesByType: { [key: string]: number };
      recentActivity: Date | null;
      suspiciousActivities: number;
    };
    sessions: {
      totalSessions: number;
      activeSessions: number;
      deviceTypes: { [key: string]: number };
      recentActivity: Date | null;
    };
  }> {
    try {
      const user = await this.getUserById(userId);
      const activityStats = await UserActivityModel.getActivityStats(userId);
      const sessionStats = await UserSessionModel.getSessionStats(userId);

      return {
        profile: {
          joinDate: user.createdAt,
          lastLogin: user.lastLoginAt,
          emailVerified: user.emailVerified,
          twoFactorEnabled: user.twoFactorEnabled,
        },
        activity: activityStats,
        sessions: sessionStats,
      };
    } catch (error) {
      logger.error('Error getting user stats:', error);
      throw error;
    }
  }

  // Search users (admin only)
  static async searchUsers(
    searchOptions: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      status?: UserStatus;
      role?: UserRole;
      search?: string;
    },
    requestingUserId: string
  ): Promise<{ users: User[]; total: number; page: number; limit: number }> {
    try {
      // Check if requesting user is admin
      const requestingUser = await this.getUserById(requestingUserId);
      if (requestingUser.role !== UserRole.ADMIN && requestingUser.role !== UserRole.SUPER_ADMIN) {
        throw new AuthorizationError('Insufficient permissions to search users');
      }

      const result = await UserModel.findMany(searchOptions);

      logger.info('Users searched', {
        requestingUserId,
        searchOptions,
        resultCount: result.users.length,
      });

      return result;
    } catch (error) {
      logger.error('Error searching users:', error);
      throw error;
    }
  }

  // Update user role (super admin only)
  static async updateUserRole(
    userId: string,
    newRole: UserRole,
    requestingUserId: string,
    ipAddress: string,
    userAgent?: string
  ): Promise<User> {
    try {
      // Check if requesting user is super admin
      const requestingUser = await this.getUserById(requestingUserId);
      if (requestingUser.role !== UserRole.SUPER_ADMIN) {
        throw new AuthorizationError('Only super admins can update user roles');
      }

      // Update user role
      const updatedUser = await UserModel.update(userId, { role: newRole });
      if (!updatedUser) {
        throw new NotFoundError('User not found');
      }

      // Update cache
      await setCache(`user:${userId}`, updatedUser, 3600);

      // Log activity
      await UserActivityModel.create({
        userId,
        activityType: UserActivityType.ROLE_CHANGE,
        description: `User role changed to ${newRole}`,
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.HIGH,
        requiresReview: true,
        metadata: {
          oldRole: (await this.getUserById(userId)).role,
          newRole,
          changedBy: requestingUserId,
        },
      });

      logger.info('User role updated', { userId, newRole, requestingUserId });

      return updatedUser;
    } catch (error) {
      logger.error('Error updating user role:', error);
      throw error;
    }
  }

  // Suspend user (admin only)
  static async suspendUser(
    userId: string,
    reason: string,
    requestingUserId: string,
    ipAddress: string,
    userAgent?: string
  ): Promise<User> {
    try {
      // Check if requesting user is admin
      const requestingUser = await this.getUserById(requestingUserId);
      if (requestingUser.role !== UserRole.ADMIN && requestingUser.role !== UserRole.SUPER_ADMIN) {
        throw new AuthorizationError('Insufficient permissions to suspend users');
      }

      // Update user status
      const updatedUser = await UserModel.update(userId, { status: UserStatus.SUSPENDED });
      if (!updatedUser) {
        throw new NotFoundError('User not found');
      }

      // Revoke all user sessions
      await UserSessionModel.revokeAllForUser(userId, 'account_suspended');

      // Update cache
      await setCache(`user:${userId}`, updatedUser, 3600);

      // Log activity
      await UserActivityModel.create({
        userId,
        activityType: UserActivityType.ACCOUNT_SUSPENDED,
        description: `Account suspended: ${reason}`,
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.HIGH,
        requiresReview: true,
        metadata: {
          reason,
          suspendedBy: requestingUserId,
        },
      });

      logger.info('User suspended', { userId, reason, requestingUserId });

      return updatedUser;
    } catch (error) {
      logger.error('Error suspending user:', error);
      throw error;
    }
  }

  // Get public profile (limited information)
  private static getPublicProfile(user: User): Partial<User> {
    return {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      avatarUrl: user.avatarUrl,
      bio: user.bio,
      createdAt: user.createdAt,
    };
  }
}
