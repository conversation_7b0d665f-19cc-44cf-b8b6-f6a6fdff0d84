import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { validate, schemas } from '../middleware/validation';
import { authenticate, optionalAuthenticate } from '../middleware/auth';
import { rateLimiters } from '../middleware/rateLimiter';

const router = Router();

// Public routes (no authentication required)
router.post(
  '/register',
  rateLimiters.auth,
  validate(schemas.userRegistration),
  AuthController.register
);

router.post(
  '/login',
  rateLimiters.auth,
  validate(schemas.userLogin),
  AuthController.login
);

router.post(
  '/refresh-token',
  rateLimiters.auth,
  AuthController.refreshToken
);

router.post(
  '/request-password-reset',
  rateLimiters.auth,
  validate(schemas.passwordResetRequest),
  AuthController.requestPasswordReset
);

router.post(
  '/reset-password',
  rateLimiters.auth,
  validate(schemas.passwordReset),
  AuthController.resetPassword
);

router.post(
  '/resend-verification',
  rateLimiters.auth,
  AuthController.resendVerification
);

router.get(
  '/verify-email/:token',
  rateLimiters.general,
  AuthController.verifyEmail
);

// Protected routes (authentication required)
router.get(
  '/me',
  rateLimiters.general,
  authenticate,
  AuthController.me
);

router.post(
  '/logout',
  rateLimiters.general,
  authenticate,
  AuthController.logout
);

router.post(
  '/logout-all',
  rateLimiters.general,
  authenticate,
  AuthController.logoutAll
);

export default router;
