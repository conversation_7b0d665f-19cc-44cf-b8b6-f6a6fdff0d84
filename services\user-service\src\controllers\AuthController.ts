import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/AuthService';
import { LoginRequest, RegisterRequest } from '@marcat/shared-types';
import { logger } from '../utils/logger';
import { ValidationError } from '../middleware/errorHandler';

export class AuthController {
  // Register a new user
  static async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const registerData: RegisterRequest = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      const result = await AuthService.register(registerData, ipAddress, userAgent);

      logger.info('User registration successful', {
        userId: result.user.id,
        email: result.user.email,
        ipAddress,
      });

      res.status(201).json({
        success: true,
        message: result.message,
        data: {
          user: {
            id: result.user.id,
            email: result.user.email,
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            role: result.user.role,
            status: result.user.status,
            emailVerified: result.user.emailVerified,
            createdAt: result.user.createdAt,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Login user
  static async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const loginData: LoginRequest = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');
      
      // Extract device information from user agent
      const deviceInfo = AuthController.parseDeviceInfo(userAgent);

      const result = await AuthService.login(loginData, ipAddress, userAgent, deviceInfo);

      logger.info('User login successful', {
        userId: result.user.id,
        email: result.user.email,
        ipAddress,
        deviceType: deviceInfo.deviceType,
      });

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: result.user.id,
            email: result.user.email,
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            role: result.user.role,
            status: result.user.status,
            emailVerified: result.user.emailVerified,
            avatarUrl: result.user.avatarUrl,
            preferences: result.user.preferences,
            lastLoginAt: result.user.lastLoginAt,
          },
          accessToken: result.accessToken,
          refreshToken: result.refreshToken,
          expiresAt: result.expiresAt,
          refreshExpiresAt: result.refreshExpiresAt,
          sessionId: result.sessionId,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Refresh access token
  static async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken } = req.body;
      
      if (!refreshToken) {
        throw new ValidationError('Refresh token is required');
      }

      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      const result = await AuthService.refreshToken(refreshToken, ipAddress, userAgent);

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          accessToken: result.accessToken,
          expiresAt: result.expiresAt,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Logout user
  static async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const sessionToken = req.headers.authorization?.replace('Bearer ', '');
      
      if (!sessionToken) {
        throw new ValidationError('Authorization token is required');
      }

      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      const result = await AuthService.logout(sessionToken, ipAddress, userAgent);

      res.json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
  }

  // Logout from all devices
  static async logoutAll(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      const result = await AuthService.logoutAll(userId, ipAddress, userAgent);

      res.json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
  }

  // Verify email
  static async verifyEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { token } = req.params;
      
      if (!token) {
        throw new ValidationError('Verification token is required');
      }

      const result = await AuthService.verifyEmail(token);

      res.json({
        success: true,
        message: result.message,
        data: {
          user: {
            id: result.user.id,
            email: result.user.email,
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            emailVerified: result.user.emailVerified,
            status: result.user.status,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Request password reset
  static async requestPasswordReset(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.body;
      
      if (!email) {
        throw new ValidationError('Email is required');
      }

      // TODO: Implement password reset request
      // const result = await AuthService.requestPasswordReset(email, ipAddress, userAgent);

      // For now, return success message without revealing if email exists
      res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.',
      });
    } catch (error) {
      next(error);
    }
  }

  // Reset password
  static async resetPassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { token, password } = req.body;
      
      if (!token || !password) {
        throw new ValidationError('Token and password are required');
      }

      // TODO: Implement password reset
      // const result = await AuthService.resetPassword(token, password, ipAddress, userAgent);

      res.json({
        success: true,
        message: 'Password reset successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  // Resend verification email
  static async resendVerification(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.body;
      
      if (!email) {
        throw new ValidationError('Email is required');
      }

      // TODO: Implement resend verification
      // const result = await AuthService.resendVerification(email, ipAddress, userAgent);

      res.json({
        success: true,
        message: 'If an account with that email exists and is not verified, a verification email has been sent.',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get current user info
  static async me(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user;
      
      if (!user) {
        throw new ValidationError('User not authenticated');
      }

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phoneNumber: user.phoneNumber,
            dateOfBirth: user.dateOfBirth,
            bio: user.bio,
            role: user.role,
            status: user.status,
            avatarUrl: user.avatarUrl,
            coverImageUrl: user.coverImageUrl,
            preferences: user.preferences,
            address: user.address,
            emailVerified: user.emailVerified,
            twoFactorEnabled: user.twoFactorEnabled,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Parse device information from user agent
  private static parseDeviceInfo(userAgent?: string): { deviceType?: string; deviceName?: string } {
    if (!userAgent) {
      return {};
    }

    const ua = userAgent.toLowerCase();
    let deviceType = 'desktop';
    let deviceName = 'Unknown Device';

    // Detect device type
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      deviceType = 'mobile';
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      deviceType = 'tablet';
    }

    // Detect device name
    if (ua.includes('iphone')) {
      deviceName = 'iPhone';
    } else if (ua.includes('ipad')) {
      deviceName = 'iPad';
    } else if (ua.includes('android')) {
      deviceName = 'Android Device';
    } else if (ua.includes('chrome')) {
      deviceName = 'Chrome Browser';
    } else if (ua.includes('firefox')) {
      deviceName = 'Firefox Browser';
    } else if (ua.includes('safari')) {
      deviceName = 'Safari Browser';
    } else if (ua.includes('edge')) {
      deviceName = 'Edge Browser';
    }

    return { deviceType, deviceName };
  }
}
