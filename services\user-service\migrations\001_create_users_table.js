/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Basic user information
    table.string('email', 255).notNullable().unique();
    table.string('password_hash', 255).notNullable();
    table.string('first_name', 100).notNullable();
    table.string('last_name', 100).notNullable();
    table.string('phone_number', 20).nullable();
    table.date('date_of_birth').nullable();
    table.text('bio').nullable();
    
    // User role and status
    table.enum('role', ['CUSTOMER', 'SELLER', 'ADMIN', 'SUPER_ADMIN']).notNullable().defaultTo('CUSTOMER');
    table.enum('status', ['PENDING_VERIFICATION', 'ACTIVE', 'SUSPENDED', 'DELETED']).notNullable().defaultTo('PENDING_VERIFICATION');
    
    // Profile and preferences
    table.string('avatar_url', 500).nullable();
    table.string('cover_image_url', 500).nullable();
    table.json('preferences').nullable(); // JSON object for user preferences
    table.json('address').nullable(); // JSON object for address information
    
    // Verification and security
    table.boolean('email_verified').notNullable().defaultTo(false);
    table.timestamp('email_verified_at').nullable();
    table.string('email_verification_token', 255).nullable();
    table.timestamp('email_verification_expires_at').nullable();
    
    // Password reset
    table.string('password_reset_token', 255).nullable();
    table.timestamp('password_reset_expires_at').nullable();
    
    // Account security
    table.boolean('two_factor_enabled').notNullable().defaultTo(false);
    table.string('two_factor_secret', 255).nullable();
    table.json('backup_codes').nullable(); // Array of backup codes
    
    // Login tracking
    table.timestamp('last_login_at').nullable();
    table.string('last_login_ip', 45).nullable(); // IPv6 support
    table.integer('failed_login_attempts').notNullable().defaultTo(0);
    table.timestamp('locked_until').nullable();
    
    // Timestamps
    table.timestamps(true, true); // created_at, updated_at
    table.timestamp('deleted_at').nullable(); // Soft delete
    
    // Indexes
    table.index(['email']);
    table.index(['status']);
    table.index(['role']);
    table.index(['created_at']);
    table.index(['email_verification_token']);
    table.index(['password_reset_token']);
    table.index(['deleted_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
