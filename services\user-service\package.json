{"name": "@marcat/user-service", "version": "1.0.0", "description": "User management and authentication service for Marcat platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "migrate:up": "knex migrate:latest", "migrate:down": "knex migrate:rollback", "migrate:make": "knex migrate:make", "seed:run": "knex seed:run", "seed:make": "knex seed:make"}, "dependencies": {"express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "knex": "^3.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "aws-sdk": "^2.1489.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.0", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}, "engines": {"node": ">=18.0.0"}}