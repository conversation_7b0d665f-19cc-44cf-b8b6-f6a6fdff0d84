import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config/config';
import { logger } from '../utils/logger';
import { AuthenticationError, AuthorizationError } from './errorHandler';
import { getCache, setCache } from '../config/redis';
import { User, UserRole, UserStatus } from '@marcat/shared-types';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
      token?: string;
    }
  }
}

// JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  status: UserStatus;
  sessionId: string;
  iat: number;
  exp: number;
}

// Token verification function
const verifyToken = async (token: string): Promise<JWTPayload> => {
  try {
    const decoded = jwt.verify(token, config.jwt.secret) as JWTPayload;
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new AuthenticationError('Token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new AuthenticationError('Invalid token');
    } else {
      throw new AuthenticationError('Token verification failed');
    }
  }
};

// Check if token is blacklisted
const isTokenBlacklisted = async (token: string): Promise<boolean> => {
  try {
    const blacklisted = await getCache<boolean>(`blacklist:${token}`);
    return blacklisted === true;
  } catch (error) {
    logger.error('Error checking token blacklist:', error);
    return false; // Fail open for availability
  }
};

// Get user from cache or database
const getUserFromCache = async (userId: string): Promise<User | null> => {
  try {
    const cachedUser = await getCache<User>(`user:${userId}`);
    if (cachedUser) {
      return cachedUser;
    }

    // TODO: Implement database lookup when user model is ready
    // const user = await UserModel.findById(userId);
    // if (user) {
    //   await setCache(`user:${userId}`, user, 300); // Cache for 5 minutes
    //   return user;
    // }

    return null;
  } catch (error) {
    logger.error('Error getting user from cache:', error);
    return null;
  }
};

// Main authentication middleware
export const authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('No token provided');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    req.token = token;

    // Check if token is blacklisted
    if (await isTokenBlacklisted(token)) {
      throw new AuthenticationError('Token has been revoked');
    }

    // Verify token
    const payload = await verifyToken(token);

    // Get user details
    const user = await getUserFromCache(payload.userId);
    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Check user status
    if (user.status === UserStatus.SUSPENDED) {
      throw new AuthenticationError('Account suspended');
    }

    if (user.status === UserStatus.DELETED) {
      throw new AuthenticationError('Account not found');
    }

    if (user.status === UserStatus.PENDING_VERIFICATION) {
      throw new AuthenticationError('Account not verified');
    }

    // Attach user to request
    req.user = user;

    // Log authentication success
    logger.debug('User authenticated successfully', {
      userId: user.id,
      email: user.email,
      role: user.role,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    next();
  } catch (error) {
    // Log authentication failure
    logger.warn('Authentication failed', {
      error: error.message,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      path: req.path,
      method: req.method,
    });

    next(error);
  }
};

// Optional authentication middleware (doesn't throw error if no token)
export const optionalAuthenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);
    req.token = token;

    // Check if token is blacklisted
    if (await isTokenBlacklisted(token)) {
      return next(); // Continue without authentication
    }

    // Verify token
    const payload = await verifyToken(token);

    // Get user details
    const user = await getUserFromCache(payload.userId);
    if (user && user.status === UserStatus.ACTIVE) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Log but don't throw error for optional authentication
    logger.debug('Optional authentication failed', {
      error: error.message,
      ip: req.ip,
      path: req.path,
    });
    next(); // Continue without authentication
  }
};

// Role-based authorization middleware
export const authorize = (...allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      if (!allowedRoles.includes(req.user.role)) {
        logger.warn('Authorization failed', {
          userId: req.user.id,
          userRole: req.user.role,
          requiredRoles: allowedRoles,
          path: req.path,
          method: req.method,
        });
        throw new AuthorizationError('Insufficient permissions');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// Resource ownership authorization
export const authorizeOwnership = (resourceUserIdField: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      // Admin and super admin can access any resource
      if (req.user.role === UserRole.ADMIN || req.user.role === UserRole.SUPER_ADMIN) {
        return next();
      }

      // Check if user owns the resource
      const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];

      if (!resourceUserId) {
        throw new AuthorizationError('Resource owner not specified');
      }

      if (req.user.id !== resourceUserId) {
        logger.warn('Ownership authorization failed', {
          userId: req.user.id,
          resourceUserId,
          path: req.path,
          method: req.method,
        });
        throw new AuthorizationError('Access denied: not resource owner');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// Blacklist token function
export const blacklistToken = async (token: string, expiresIn?: number): Promise<void> => {
  try {
    const ttl = expiresIn || config.jwt.expiresIn;
    await setCache(`blacklist:${token}`, true, ttl);

    logger.info('Token blacklisted', {
      tokenHash: token.substring(0, 10) + '...',
      ttl,
    });
  } catch (error) {
    logger.error('Error blacklisting token:', error);
    throw error;
  }
};