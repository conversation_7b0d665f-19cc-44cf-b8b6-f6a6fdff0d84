import { db } from '../config/database';
import { User, UserRole, UserStatus, UserPreferences, Address } from '@marcat/shared-types';
import { logger } from '../utils/logger';

export class UserModel {
  private static tableName = 'users';

  // Create a new user
  static async create(userData: Partial<User>): Promise<User> {
    try {
      const [user] = await db(this.tableName)
        .insert({
          ...userData,
          created_at: new Date(),
          updated_at: new Date(),
        })
        .returning('*');
      
      logger.info('User created successfully', { userId: user.id, email: user.email });
      return this.mapDbUserToUser(user);
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  // Find user by ID
  static async findById(id: string): Promise<User | null> {
    try {
      const user = await db(this.tableName)
        .where({ id })
        .whereNull('deleted_at')
        .first();
      
      return user ? this.mapDbUserToUser(user) : null;
    } catch (error) {
      logger.error('Error finding user by ID:', error);
      throw error;
    }
  }

  // Find user by email
  static async findByEmail(email: string): Promise<User | null> {
    try {
      const user = await db(this.tableName)
        .where({ email: email.toLowerCase() })
        .whereNull('deleted_at')
        .first();
      
      return user ? this.mapDbUserToUser(user) : null;
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw error;
    }
  }

  // Update user
  static async update(id: string, updateData: Partial<User>): Promise<User | null> {
    try {
      const [user] = await db(this.tableName)
        .where({ id })
        .whereNull('deleted_at')
        .update({
          ...updateData,
          updated_at: new Date(),
        })
        .returning('*');
      
      if (user) {
        logger.info('User updated successfully', { userId: id });
        return this.mapDbUserToUser(user);
      }
      
      return null;
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  // Soft delete user
  static async softDelete(id: string): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ id })
        .whereNull('deleted_at')
        .update({
          status: UserStatus.DELETED,
          deleted_at: new Date(),
          updated_at: new Date(),
        });
      
      if (result > 0) {
        logger.info('User soft deleted successfully', { userId: id });
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Error soft deleting user:', error);
      throw error;
    }
  }

  // Find users with pagination and filters
  static async findMany(options: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    status?: UserStatus;
    role?: UserRole;
    search?: string;
  } = {}): Promise<{ users: User[]; total: number; page: number; limit: number }> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc',
        status,
        role,
        search,
      } = options;

      const offset = (page - 1) * limit;
      
      let query = db(this.tableName).whereNull('deleted_at');
      
      // Apply filters
      if (status) {
        query = query.where({ status });
      }
      
      if (role) {
        query = query.where({ role });
      }
      
      if (search) {
        query = query.where(function() {
          this.where('first_name', 'ilike', `%${search}%`)
            .orWhere('last_name', 'ilike', `%${search}%`)
            .orWhere('email', 'ilike', `%${search}%`);
        });
      }
      
      // Get total count
      const [{ count }] = await query.clone().count('* as count');
      const total = parseInt(count as string);
      
      // Get paginated results
      const dbUsers = await query
        .orderBy(sortBy, sortOrder)
        .limit(limit)
        .offset(offset);
      
      const users = dbUsers.map(user => this.mapDbUserToUser(user));
      
      return {
        users,
        total,
        page,
        limit,
      };
    } catch (error) {
      logger.error('Error finding users:', error);
      throw error;
    }
  }

  // Verify email
  static async verifyEmail(id: string): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ id })
        .update({
          email_verified: true,
          email_verified_at: new Date(),
          email_verification_token: null,
          email_verification_expires_at: null,
          status: UserStatus.ACTIVE,
          updated_at: new Date(),
        });
      
      if (result > 0) {
        logger.info('User email verified successfully', { userId: id });
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Error verifying user email:', error);
      throw error;
    }
  }

  // Update password
  static async updatePassword(id: string, passwordHash: string): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ id })
        .update({
          password_hash: passwordHash,
          password_reset_token: null,
          password_reset_expires_at: null,
          updated_at: new Date(),
        });
      
      if (result > 0) {
        logger.info('User password updated successfully', { userId: id });
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Error updating user password:', error);
      throw error;
    }
  }

  // Update login tracking
  static async updateLoginTracking(id: string, ipAddress: string): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ id })
        .update({
          last_login_at: new Date(),
          last_login_ip: ipAddress,
          failed_login_attempts: 0,
          locked_until: null,
          updated_at: new Date(),
        });
      
      return result > 0;
    } catch (error) {
      logger.error('Error updating login tracking:', error);
      throw error;
    }
  }

  // Increment failed login attempts
  static async incrementFailedLoginAttempts(id: string): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ id })
        .increment('failed_login_attempts', 1)
        .update({ updated_at: new Date() });
      
      return result > 0;
    } catch (error) {
      logger.error('Error incrementing failed login attempts:', error);
      throw error;
    }
  }

  // Lock user account
  static async lockAccount(id: string, lockDuration: number): Promise<boolean> {
    try {
      const lockedUntil = new Date(Date.now() + lockDuration);
      
      const result = await db(this.tableName)
        .where({ id })
        .update({
          locked_until: lockedUntil,
          updated_at: new Date(),
        });
      
      if (result > 0) {
        logger.warn('User account locked', { userId: id, lockedUntil });
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Error locking user account:', error);
      throw error;
    }
  }

  // Map database user to User interface
  private static mapDbUserToUser(dbUser: any): User {
    return {
      id: dbUser.id,
      email: dbUser.email,
      firstName: dbUser.first_name,
      lastName: dbUser.last_name,
      phoneNumber: dbUser.phone_number,
      dateOfBirth: dbUser.date_of_birth,
      bio: dbUser.bio,
      role: dbUser.role as UserRole,
      status: dbUser.status as UserStatus,
      avatarUrl: dbUser.avatar_url,
      coverImageUrl: dbUser.cover_image_url,
      preferences: dbUser.preferences as UserPreferences,
      address: dbUser.address as Address,
      emailVerified: dbUser.email_verified,
      emailVerifiedAt: dbUser.email_verified_at,
      twoFactorEnabled: dbUser.two_factor_enabled,
      lastLoginAt: dbUser.last_login_at,
      lastLoginIp: dbUser.last_login_ip,
      failedLoginAttempts: dbUser.failed_login_attempts,
      lockedUntil: dbUser.locked_until,
      createdAt: dbUser.created_at,
      updatedAt: dbUser.updated_at,
    };
  }
}
