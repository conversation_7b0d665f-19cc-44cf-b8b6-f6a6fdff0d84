/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('password_resets', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // User information (email for lookup, user_id for reference)
    table.string('email', 255).notNullable();
    table.uuid('user_id').nullable(); // Nullable in case user is deleted
    table.foreign('user_id').references('id').inTable('users').onDelete('SET NULL');
    
    // Reset token information
    table.string('token', 255).notNullable().unique();
    table.timestamp('expires_at').notNullable();
    
    // Usage tracking
    table.boolean('used').notNullable().defaultTo(false);
    table.timestamp('used_at').nullable();
    table.string('used_ip', 45).nullable();
    
    // Request information
    table.string('request_ip', 45).notNullable();
    table.string('user_agent', 500).nullable();
    table.string('country', 100).nullable();
    table.string('city', 100).nullable();
    
    // Security tracking
    table.integer('attempt_count').notNullable().defaultTo(0);
    table.timestamp('last_attempt_at').nullable();
    
    // Timestamps
    table.timestamps(true, true); // created_at, updated_at
    
    // Indexes
    table.index(['email']);
    table.index(['user_id']);
    table.index(['token']);
    table.index(['expires_at']);
    table.index(['used']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('password_resets');
};
