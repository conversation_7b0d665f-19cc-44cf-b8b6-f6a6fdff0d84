import { db } from '../config/database';
import { UserActivity, UserActivityType, RiskLevel } from '@marcat/shared-types';
import { logger } from '../utils/logger';

export class UserActivityModel {
  private static tableName = 'user_activities';

  // Create a new activity record
  static async create(activityData: {
    userId: string;
    activityType: UserActivityType;
    description: string;
    metadata?: any;
    ipAddress: string;
    userAgent?: string;
    country?: string;
    city?: string;
    riskLevel?: RiskLevel;
    requiresReview?: boolean;
  }): Promise<UserActivity> {
    try {
      const [activity] = await db(this.tableName)
        .insert({
          user_id: activityData.userId,
          activity_type: activityData.activityType,
          description: activityData.description,
          metadata: activityData.metadata ? JSON.stringify(activityData.metadata) : null,
          ip_address: activityData.ipAddress,
          user_agent: activityData.userAgent,
          country: activityData.country,
          city: activityData.city,
          risk_level: activityData.riskLevel || RiskLevel.LOW,
          requires_review: activityData.requiresReview || false,
          created_at: new Date(),
        })
        .returning('*');
      
      logger.debug('User activity recorded', {
        activityId: activity.id,
        userId: activityData.userId,
        activityType: activityData.activityType,
        riskLevel: activityData.riskLevel,
      });
      
      return this.mapDbActivityToActivity(activity);
    } catch (error) {
      logger.error('Error creating user activity:', error);
      throw error;
    }
  }

  // Find activities by user ID
  static async findByUserId(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      activityType?: UserActivityType;
      riskLevel?: RiskLevel;
      requiresReview?: boolean;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<{ activities: UserActivity[]; total: number; page: number; limit: number }> {
    try {
      const {
        page = 1,
        limit = 20,
        activityType,
        riskLevel,
        requiresReview,
        startDate,
        endDate,
      } = options;

      const offset = (page - 1) * limit;
      
      let query = db(this.tableName).where({ user_id: userId });
      
      // Apply filters
      if (activityType) {
        query = query.where({ activity_type: activityType });
      }
      
      if (riskLevel) {
        query = query.where({ risk_level: riskLevel });
      }
      
      if (requiresReview !== undefined) {
        query = query.where({ requires_review: requiresReview });
      }
      
      if (startDate) {
        query = query.where('created_at', '>=', startDate);
      }
      
      if (endDate) {
        query = query.where('created_at', '<=', endDate);
      }
      
      // Get total count
      const [{ count }] = await query.clone().count('* as count');
      const total = parseInt(count as string);
      
      // Get paginated results
      const dbActivities = await query
        .orderBy('created_at', 'desc')
        .limit(limit)
        .offset(offset);
      
      const activities = dbActivities.map(activity => this.mapDbActivityToActivity(activity));
      
      return {
        activities,
        total,
        page,
        limit,
      };
    } catch (error) {
      logger.error('Error finding activities by user ID:', error);
      throw error;
    }
  }

  // Find activities requiring review
  static async findRequiringReview(options: {
    page?: number;
    limit?: number;
    riskLevel?: RiskLevel;
  } = {}): Promise<{ activities: UserActivity[]; total: number; page: number; limit: number }> {
    try {
      const { page = 1, limit = 20, riskLevel } = options;
      const offset = (page - 1) * limit;
      
      let query = db(this.tableName)
        .where({ requires_review: true })
        .whereNull('reviewed_at');
      
      if (riskLevel) {
        query = query.where({ risk_level: riskLevel });
      }
      
      // Get total count
      const [{ count }] = await query.clone().count('* as count');
      const total = parseInt(count as string);
      
      // Get paginated results
      const dbActivities = await query
        .orderBy('created_at', 'desc')
        .limit(limit)
        .offset(offset);
      
      const activities = dbActivities.map(activity => this.mapDbActivityToActivity(activity));
      
      return {
        activities,
        total,
        page,
        limit,
      };
    } catch (error) {
      logger.error('Error finding activities requiring review:', error);
      throw error;
    }
  }

  // Mark activity as reviewed
  static async markAsReviewed(activityId: string, reviewedBy: string): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ id: activityId })
        .update({
          reviewed_at: new Date(),
          reviewed_by: reviewedBy,
        });
      
      if (result > 0) {
        logger.info('Activity marked as reviewed', { activityId, reviewedBy });
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Error marking activity as reviewed:', error);
      throw error;
    }
  }

  // Get activity statistics for a user
  static async getActivityStats(userId: string, days: number = 30): Promise<{
    totalActivities: number;
    activitiesByType: { [key: string]: number };
    activitiesByRisk: { [key: string]: number };
    recentActivity: Date | null;
    suspiciousActivities: number;
  }> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      
      // Total activities
      const [{ count: totalActivities }] = await db(this.tableName)
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .count('* as count');
      
      // Activities by type
      const typeResults = await db(this.tableName)
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .select('activity_type')
        .count('* as count')
        .groupBy('activity_type');
      
      const activitiesByType: { [key: string]: number } = {};
      typeResults.forEach(result => {
        activitiesByType[result.activity_type] = parseInt(result.count as string);
      });
      
      // Activities by risk level
      const riskResults = await db(this.tableName)
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .select('risk_level')
        .count('* as count')
        .groupBy('risk_level');
      
      const activitiesByRisk: { [key: string]: number } = {};
      riskResults.forEach(result => {
        activitiesByRisk[result.risk_level] = parseInt(result.count as string);
      });
      
      // Suspicious activities (high risk or requiring review)
      const [{ count: suspiciousActivities }] = await db(this.tableName)
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .where(function() {
          this.where({ risk_level: RiskLevel.HIGH })
            .orWhere({ requires_review: true });
        })
        .count('* as count');
      
      // Recent activity
      const recentActivity = await db(this.tableName)
        .where({ user_id: userId })
        .orderBy('created_at', 'desc')
        .first();
      
      return {
        totalActivities: parseInt(totalActivities as string),
        activitiesByType,
        activitiesByRisk,
        recentActivity: recentActivity?.created_at || null,
        suspiciousActivities: parseInt(suspiciousActivities as string),
      };
    } catch (error) {
      logger.error('Error getting activity stats:', error);
      throw error;
    }
  }

  // Get system-wide activity statistics
  static async getSystemStats(days: number = 30): Promise<{
    totalActivities: number;
    activitiesByType: { [key: string]: number };
    activitiesByRisk: { [key: string]: number };
    pendingReviews: number;
    topIpAddresses: { ip: string; count: number }[];
  }> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      
      // Total activities
      const [{ count: totalActivities }] = await db(this.tableName)
        .where('created_at', '>=', startDate)
        .count('* as count');
      
      // Activities by type
      const typeResults = await db(this.tableName)
        .where('created_at', '>=', startDate)
        .select('activity_type')
        .count('* as count')
        .groupBy('activity_type');
      
      const activitiesByType: { [key: string]: number } = {};
      typeResults.forEach(result => {
        activitiesByType[result.activity_type] = parseInt(result.count as string);
      });
      
      // Activities by risk level
      const riskResults = await db(this.tableName)
        .where('created_at', '>=', startDate)
        .select('risk_level')
        .count('* as count')
        .groupBy('risk_level');
      
      const activitiesByRisk: { [key: string]: number } = {};
      riskResults.forEach(result => {
        activitiesByRisk[result.risk_level] = parseInt(result.count as string);
      });
      
      // Pending reviews
      const [{ count: pendingReviews }] = await db(this.tableName)
        .where({ requires_review: true })
        .whereNull('reviewed_at')
        .count('* as count');
      
      // Top IP addresses
      const ipResults = await db(this.tableName)
        .where('created_at', '>=', startDate)
        .select('ip_address')
        .count('* as count')
        .groupBy('ip_address')
        .orderBy('count', 'desc')
        .limit(10);
      
      const topIpAddresses = ipResults.map(result => ({
        ip: result.ip_address,
        count: parseInt(result.count as string),
      }));
      
      return {
        totalActivities: parseInt(totalActivities as string),
        activitiesByType,
        activitiesByRisk,
        pendingReviews: parseInt(pendingReviews as string),
        topIpAddresses,
      };
    } catch (error) {
      logger.error('Error getting system stats:', error);
      throw error;
    }
  }

  // Map database activity to UserActivity interface
  private static mapDbActivityToActivity(dbActivity: any): UserActivity {
    return {
      id: dbActivity.id,
      userId: dbActivity.user_id,
      activityType: dbActivity.activity_type as UserActivityType,
      description: dbActivity.description,
      metadata: dbActivity.metadata ? JSON.parse(dbActivity.metadata) : null,
      ipAddress: dbActivity.ip_address,
      userAgent: dbActivity.user_agent,
      country: dbActivity.country,
      city: dbActivity.city,
      riskLevel: dbActivity.risk_level as RiskLevel,
      requiresReview: dbActivity.requires_review,
      reviewedAt: dbActivity.reviewed_at,
      reviewedBy: dbActivity.reviewed_by,
      createdAt: dbActivity.created_at,
    };
  }
}
