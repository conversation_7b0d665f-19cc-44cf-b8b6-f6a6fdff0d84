import { BaseEntity, Address, Image, ContactInfo, SocialMedia, BusinessHours, Money } from './common';

export interface Store extends BaseEntity {
  name: string;
  slug: string;
  description: string;
  ownerId: string;
  logo?: Image;
  banner?: Image;
  address: Address;
  contact: ContactInfo;
  socialMedia?: SocialMedia;
  businessHours: BusinessHours;
  isActive: boolean;
  isVerified: boolean;
  rating: number;
  reviewCount: number;
  totalSales: number;
  commission: StoreCommission;
  settings: StoreSettings;
  metadata?: Record<string, any>;
}

export interface StoreCommission {
  type: 'percentage' | 'fixed';
  value: number;
  minimumAmount?: Money;
  maximumAmount?: Money;
}

export interface StoreSettings {
  allowReviews: boolean;
  autoApproveProducts: boolean;
  requireProductApproval: boolean;
  allowBackorders: boolean;
  showInventoryCount: boolean;
  enableWishlist: boolean;
  enableCompare: boolean;
  taxSettings: TaxSettings;
  shippingSettings: ShippingSettings;
  returnPolicy: ReturnPolicy;
}

export interface TaxSettings {
  enabled: boolean;
  taxRate: number;
  taxInclusive: boolean;
  taxNumber?: string;
}

export interface ShippingSettings {
  freeShippingThreshold?: Money;
  defaultShippingCost: Money;
  shippingZones: ShippingZone[];
}

export interface ShippingZone {
  id: string;
  name: string;
  countries: string[];
  shippingMethods: ShippingMethod[];
}

export interface ShippingMethod {
  id: string;
  name: string;
  description?: string;
  cost: Money;
  estimatedDays: number;
  trackingEnabled: boolean;
}

export interface ReturnPolicy {
  enabled: boolean;
  returnWindow: number; // days
  conditions: string[];
  restockingFee?: Money;
}

export interface CreateStoreRequest {
  name: string;
  description: string;
  address: Address;
  contact: ContactInfo;
  businessHours: BusinessHours;
  socialMedia?: SocialMedia;
}

export interface UpdateStoreRequest {
  name?: string;
  description?: string;
  address?: Address;
  contact?: ContactInfo;
  businessHours?: BusinessHours;
  socialMedia?: SocialMedia;
  settings?: Partial<StoreSettings>;
}

export interface StoreStats {
  totalProducts: number;
  activeProducts: number;
  totalOrders: number;
  totalRevenue: Money;
  averageOrderValue: Money;
  conversionRate: number;
  topCategories: CategoryStats[];
  recentOrders: number;
  pendingOrders: number;
}

export interface CategoryStats {
  categoryId: string;
  categoryName: string;
  productCount: number;
  revenue: Money;
  orderCount: number;
}

export interface StoreAnalytics {
  period: 'day' | 'week' | 'month' | 'year';
  sales: SalesData[];
  visitors: VisitorData[];
  topProducts: ProductPerformance[];
  customerInsights: CustomerInsights;
}

export interface SalesData {
  date: string;
  revenue: Money;
  orders: number;
  averageOrderValue: Money;
}

export interface VisitorData {
  date: string;
  visitors: number;
  pageViews: number;
  bounceRate: number;
  conversionRate: number;
}

export interface ProductPerformance {
  productId: string;
  productName: string;
  sales: number;
  revenue: Money;
  views: number;
  conversionRate: number;
}

export interface CustomerInsights {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  averageLifetimeValue: Money;
  topCustomers: TopCustomer[];
}

export interface TopCustomer {
  customerId: string;
  customerName: string;
  totalOrders: number;
  totalSpent: Money;
  lastOrderDate: Date;
}

export interface StoreVerification {
  id: string;
  storeId: string;
  status: 'pending' | 'approved' | 'rejected';
  documents: VerificationDocument[];
  submittedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  notes?: string;
}

export interface VerificationDocument {
  type: 'business_license' | 'tax_certificate' | 'identity_proof' | 'address_proof';
  url: string;
  fileName: string;
  uploadedAt: Date;
}

export interface StoreSubscription {
  id: string;
  storeId: string;
  plan: SubscriptionPlan;
  status: 'active' | 'cancelled' | 'expired' | 'suspended';
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  features: string[];
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: Money;
  billingCycle: 'monthly' | 'yearly';
  features: PlanFeature[];
  limits: PlanLimits;
}

export interface PlanFeature {
  name: string;
  description: string;
  included: boolean;
}

export interface PlanLimits {
  maxProducts: number;
  maxOrders: number;
  maxStorage: number; // in MB
  maxBandwidth: number; // in GB
}