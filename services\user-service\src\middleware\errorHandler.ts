import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { config } from '../config/config';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
  code?: string;
}

export class CustomError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Specific error classes
export class ValidationError extends CustomError {
  constructor(message: string, field?: string) {
    super(message, 400, true, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends CustomError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends CustomError {
  constructor(message: string = 'Access denied') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends CustomError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, true, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends CustomError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class DatabaseError extends CustomError {
  constructor(message: string = 'Database error') {
    super(message, 500, true, 'DATABASE_ERROR');
    this.name = 'DatabaseError';
  }
}

export class ExternalServiceError extends CustomError {
  constructor(message: string = 'External service error') {
    super(message, 502, true, 'EXTERNAL_SERVICE_ERROR');
    this.name = 'ExternalServiceError';
  }
}

// Error response interface
interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    statusCode: number;
    timestamp: string;
    path: string;
    method: string;
    requestId?: string;
    details?: any;
    stack?: string;
  };
}

// Helper function to determine if error is operational
const isOperationalError = (error: AppError): boolean => {
  return error.isOperational === true;
};

// Helper function to create error response
const createErrorResponse = (
  error: AppError,
  req: Request,
  includeStack: boolean = false
): ErrorResponse => {
  return {
    success: false,
    error: {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode || 500,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method,
      requestId: req.headers['x-request-id'] as string,
      details: error.name !== 'Error' ? { name: error.name } : undefined,
      stack: includeStack ? error.stack : undefined,
    },
  };
};

// Main error handler middleware
export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Set default error properties
  error.statusCode = error.statusCode || 500;
  error.isOperational = error.isOperational !== undefined ? error.isOperational : false;

  // Log the error
  const logLevel = error.statusCode >= 500 ? 'error' : 'warn';
  logger.log(logLevel, `${error.message}`, {
    error: {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      stack: error.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      path: req.path,
      query: req.query,
      params: req.params,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: (req as any).user?.id,
    },
  });

  // Handle specific error types
  if (error.name === 'ValidationError') {
    error.statusCode = 400;
    error.code = 'VALIDATION_ERROR';
  } else if (error.name === 'CastError') {
    error.statusCode = 400;
    error.code = 'INVALID_ID';
    error.message = 'Invalid ID format';
  } else if (error.name === 'JsonWebTokenError') {
    error.statusCode = 401;
    error.code = 'INVALID_TOKEN';
    error.message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    error.statusCode = 401;
    error.code = 'TOKEN_EXPIRED';
    error.message = 'Token expired';
  } else if (error.code === '23505') { // PostgreSQL unique violation
    error.statusCode = 409;
    error.code = 'DUPLICATE_ENTRY';
    error.message = 'Resource already exists';
  } else if (error.code === '23503') { // PostgreSQL foreign key violation
    error.statusCode = 400;
    error.code = 'FOREIGN_KEY_VIOLATION';
    error.message = 'Referenced resource does not exist';
  }

  // Create error response
  const includeStack = config.nodeEnv === 'development';
  const errorResponse = createErrorResponse(error, req, includeStack);

  // Send error response
  res.status(error.statusCode).json(errorResponse);
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Promise Rejection:', {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise,
  });

  // Close server gracefully
  process.exit(1);
});

// Uncaught exception handler
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception:', {
    error: error.message,
    stack: error.stack,
  });

  // Close server gracefully
  process.exit(1);
});