import { Router, Request, Response } from 'express';
import { checkDatabaseHealth } from '../config/database';
import { checkRedisHealth } from '../config/redis';
import { logger } from '../utils/logger';
import { config } from '../config/config';

const router = Router();

// Health check response interface
interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: {
      status: 'healthy' | 'unhealthy';
      responseTime?: number;
      error?: string;
    };
    redis: {
      status: 'healthy' | 'unhealthy';
      responseTime?: number;
      error?: string;
    };
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
}

// Get memory usage
const getMemoryUsage = () => {
  const memUsage = process.memoryUsage();
  const totalMemory = require('os').totalmem();
  const usedMemory = memUsage.heapUsed;

  return {
    used: Math.round(usedMemory / 1024 / 1024), // MB
    total: Math.round(totalMemory / 1024 / 1024), // MB
    percentage: Math.round((usedMemory / totalMemory) * 100),
  };
};

// Get CPU usage (simplified)
const getCpuUsage = () => {
  const cpus = require('os').cpus();
  let totalIdle = 0;
  let totalTick = 0;

  cpus.forEach((cpu: any) => {
    for (const type in cpu.times) {
      totalTick += cpu.times[type];
    }
    totalIdle += cpu.times.idle;
  });

  const idle = totalIdle / cpus.length;
  const total = totalTick / cpus.length;
  const usage = 100 - ~~(100 * idle / total);

  return { usage };
};

// Basic health check - lightweight
router.get('/', async (req: Request, res: Response) => {
  try {
    const response = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: process.env.npm_package_version || '1.0.0',
      environment: config.nodeEnv,
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Basic health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

// Detailed health check - includes dependencies
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();

    // Check database health
    const dbStartTime = Date.now();
    let databaseHealth;
    try {
      const isDbHealthy = await checkDatabaseHealth();
      databaseHealth = {
        status: isDbHealthy ? 'healthy' : 'unhealthy',
        responseTime: Date.now() - dbStartTime,
      };
    } catch (error) {
      databaseHealth = {
        status: 'unhealthy' as const,
        responseTime: Date.now() - dbStartTime,
        error: error.message,
      };
    }

    // Check Redis health
    const redisStartTime = Date.now();
    let redisHealth;
    try {
      const isRedisHealthy = await checkRedisHealth();
      redisHealth = {
        status: isRedisHealthy ? 'healthy' : 'unhealthy',
        responseTime: Date.now() - redisStartTime,
      };
    } catch (error) {
      redisHealth = {
        status: 'unhealthy' as const,
        responseTime: Date.now() - redisStartTime,
        error: error.message,
      };
    }

    // Determine overall status
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    if (databaseHealth.status === 'unhealthy' || redisHealth.status === 'unhealthy') {
      overallStatus = 'unhealthy';
    } else if (databaseHealth.responseTime > 1000 || redisHealth.responseTime > 1000) {
      overallStatus = 'degraded';
    }

    const response: HealthCheckResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: process.env.npm_package_version || '1.0.0',
      environment: config.nodeEnv,
      services: {
        database: databaseHealth,
        redis: redisHealth,
      },
      memory: getMemoryUsage(),
      cpu: getCpuUsage(),
    };

    const statusCode = overallStatus === 'healthy' ? 200 :
                      overallStatus === 'degraded' ? 200 : 503;

    res.status(statusCode).json(response);

    // Log health check results
    logger.debug('Detailed health check completed', {
      status: overallStatus,
      responseTime: Date.now() - startTime,
      services: {
        database: databaseHealth.status,
        redis: redisHealth.status,
      },
    });

  } catch (error) {
    logger.error('Detailed health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

// Kubernetes liveness probe
router.get('/live', async (req: Request, res: Response) => {
  try {
    // Basic liveness check - just verify the service is running
    res.status(200).json({
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
    });
  } catch (error) {
    logger.error('Liveness check failed:', error);
    res.status(503).json({
      status: 'dead',
      timestamp: new Date().toISOString(),
    });
  }
});

// Kubernetes readiness probe
router.get('/ready', async (req: Request, res: Response) => {
  try {
    // Check if service is ready to accept traffic
    const isDatabaseReady = await checkDatabaseHealth();
    const isRedisReady = await checkRedisHealth();

    if (isDatabaseReady && isRedisReady) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
        services: {
          database: 'ready',
          redis: 'ready',
        },
      });
    } else {
      res.status(503).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        services: {
          database: isDatabaseReady ? 'ready' : 'not_ready',
          redis: isRedisReady ? 'ready' : 'not_ready',
        },
      });
    }
  } catch (error) {
    logger.error('Readiness check failed:', error);
    res.status(503).json({
      status: 'not_ready',
      timestamp: new Date().toISOString(),
      error: 'Readiness check failed',
    });
  }
});

export default router;