import { BaseEntity, UserRole, Address, Image } from './common';

export interface User extends BaseEntity {
  email: string;
  username?: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isActive: boolean;
  avatar?: Image;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other';
  preferences: UserPreferences;
  addresses: Address[];
  lastLoginAt?: Date;
  metadata?: Record<string, any>;
}

export interface UserPreferences {
  language: string;
  currency: string;
  timezone: string;
  notifications: NotificationPreferences;
  privacy: PrivacySettings;
  marketing: MarketingPreferences;
}

export interface NotificationPreferences {
  email: {
    orderUpdates: boolean;
    promotions: boolean;
    newsletter: boolean;
    security: boolean;
  };
  sms: {
    orderUpdates: boolean;
    promotions: boolean;
    security: boolean;
  };
  push: {
    orderUpdates: boolean;
    promotions: boolean;
    recommendations: boolean;
  };
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private';
  showOnlineStatus: boolean;
  allowDataCollection: boolean;
  allowPersonalization: boolean;
}

export interface MarketingPreferences {
  allowEmailMarketing: boolean;
  allowSmsMarketing: boolean;
  allowPushMarketing: boolean;
  interests: string[];
  preferredBrands: string[];
}

export interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: UserRole;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other';
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other';
  preferences?: Partial<UserPreferences>;
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface VerifyEmailRequest {
  token: string;
}

export interface VerifyPhoneRequest {
  code: string;
}

export interface UserProfile {
  id: string;
  email: string;
  username?: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
  avatar?: Image;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other';
  joinedAt: Date;
  lastActiveAt?: Date;
}

export interface UserSession {
  id: string;
  userId: string;
  deviceId?: string;
  deviceName?: string;
  ipAddress: string;
  userAgent: string;
  location?: string;
  isActive: boolean;
  createdAt: Date;
  lastActiveAt: Date;
  expiresAt: Date;
}

export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  resource?: string;
  resourceId?: string;
  metadata?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
}

export interface UserStats {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  favoriteCategories: string[];
  lastOrderDate?: Date;
  loyaltyPoints: number;
  reviewsCount: number;
  averageRating: number;
}