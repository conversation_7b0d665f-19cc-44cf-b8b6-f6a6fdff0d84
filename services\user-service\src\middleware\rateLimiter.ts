import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { config } from '../config/config';
import { logger } from '../utils/logger';
import { getRedisClient } from '../config/redis';

// Custom store using Redis for distributed rate limiting
class RedisStore {
  private client: any;
  private prefix: string;

  constructor(prefix: string = 'rl:') {
    this.prefix = prefix;
  }

  async increment(key: string): Promise<{ totalHits: number; resetTime?: Date }> {
    try {
      if (!this.client) {
        this.client = getRedisClient();
      }

      const redisKey = `${this.prefix}${key}`;
      const window = config.rateLimit.windowMs;
      const limit = config.rateLimit.max;

      // Use Redis pipeline for atomic operations
      const pipeline = this.client.multi();
      pipeline.incr(redisKey);
      pipeline.expire(redisKey, Math.ceil(window / 1000));

      const results = await pipeline.exec();
      const totalHits = results[0][1];

      const resetTime = new Date(Date.now() + window);

      return { totalHits, resetTime };
    } catch (error) {
      logger.error('Redis rate limiter error:', error);
      // Fallback to allowing the request if Redis fails
      return { totalHits: 1 };
    }
  }

  async decrement(key: string): Promise<void> {
    try {
      if (!this.client) {
        this.client = getRedisClient();
      }

      const redisKey = `${this.prefix}${key}`;
      await this.client.decr(redisKey);
    } catch (error) {
      logger.error('Redis rate limiter decrement error:', error);
    }
  }

  async resetKey(key: string): Promise<void> {
    try {
      if (!this.client) {
        this.client = getRedisClient();
      }

      const redisKey = `${this.prefix}${key}`;
      await this.client.del(redisKey);
    } catch (error) {
      logger.error('Redis rate limiter reset error:', error);
    }
  }
}

// Key generator function
const keyGenerator = (req: Request): string => {
  // Use user ID if authenticated, otherwise use IP
  const userId = (req as any).user?.id;
  if (userId) {
    return `user:${userId}`;
  }
  return `ip:${req.ip}`;
};

// Custom handler for rate limit exceeded
const rateLimitHandler = (req: Request, res: Response): void => {
  const userId = (req as any).user?.id;
  const identifier = userId ? `user:${userId}` : `ip:${req.ip}`;

  logger.warn('Rate limit exceeded', {
    identifier,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    path: req.path,
    method: req.method,
  });

  res.status(429).json({
    success: false,
    error: {
      message: 'Too many requests, please try again later',
      code: 'RATE_LIMIT_EXCEEDED',
      statusCode: 429,
      timestamp: new Date().toISOString(),
      retryAfter: Math.ceil(config.rateLimit.windowMs / 1000),
    },
  });
};

// Skip function for certain conditions
const skipFunction = (req: Request): boolean => {
  // Skip rate limiting for health checks
  if (req.path === '/health' || req.path === '/health/ready' || req.path === '/health/live') {
    return true;
  }

  // Skip for internal service calls (if they have a special header)
  if (req.headers['x-internal-service'] === 'true') {
    return true;
  }

  return false;
};

// Main rate limiter
export const rateLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  message: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator,
  skip: skipFunction,
  store: new RedisStore(),
});

// Stricter rate limiter for authentication endpoints
export const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: (req: Request, res: Response) => {
    logger.warn('Auth rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      method: req.method,
    });

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many authentication attempts, please try again later',
        code: 'AUTH_RATE_LIMIT_EXCEEDED',
        statusCode: 429,
        timestamp: new Date().toISOString(),
        retryAfter: 900, // 15 minutes
      },
    });
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => `auth:${req.ip}`,
  store: new RedisStore('auth_rl:'),
});

// Password reset rate limiter
export const passwordResetRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 attempts per hour
  message: (req: Request, res: Response) => {
    logger.warn('Password reset rate limit exceeded', {
      ip: req.ip,
      email: req.body.email,
      userAgent: req.get('User-Agent'),
    });

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many password reset attempts, please try again later',
        code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
        statusCode: 429,
        timestamp: new Date().toISOString(),
        retryAfter: 3600, // 1 hour
      },
    });
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => `pwd_reset:${req.body.email || req.ip}`,
  store: new RedisStore('pwd_reset_rl:'),
});

// Email verification rate limiter
export const emailVerificationRateLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 3, // 3 attempts per 10 minutes
  message: (req: Request, res: Response) => {
    logger.warn('Email verification rate limit exceeded', {
      ip: req.ip,
      userId: (req as any).user?.id,
      userAgent: req.get('User-Agent'),
    });

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many email verification attempts, please try again later',
        code: 'EMAIL_VERIFICATION_RATE_LIMIT_EXCEEDED',
        statusCode: 429,
        timestamp: new Date().toISOString(),
        retryAfter: 600, // 10 minutes
      },
    });
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => `email_verify:${(req as any).user?.id || req.ip}`,
  store: new RedisStore('email_verify_rl:'),
});