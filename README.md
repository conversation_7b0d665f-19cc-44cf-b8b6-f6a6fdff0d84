# Marcat - Multi-Store E-commerce Platform

## Overview
Marcat is a comprehensive, production-ready multi-store e-commerce platform specializing in men's clothing. The platform serves as a one-stop solution for buyers and sellers, featuring robust catalog management, advanced user interactions, and full e-commerce capabilities.

## Architecture

### Microservices Architecture
The platform follows a microservices architecture with the following core services:

1. **API Gateway** - Central entry point for all client requests
2. **User Service** - Authentication, authorization, and user management
3. **Store Service** - Store management and seller operations
4. **Product Service** - Product catalog, variants, and categorization
5. **Order Service** - Order processing and management
6. **Payment Service** - Payment processing and transaction handling
7. **Inventory Service** - Stock management and tracking
8. **Notification Service** - Email, SMS, and in-app notifications
9. **Analytics Service** - Real-time analytics and reporting
10. **Review Service** - Product and store reviews and ratings
11. **Support Service** - Customer support and ticketing

### Technology Stack

#### Backend
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js with Helmet for security
- **Database**: PostgreSQL (separate database per service)
- **Message Queue**: Redis for caching and pub/sub
- **Authentication**: JWT with refresh tokens
- **API Documentation**: OpenAPI/Swagger

#### Frontend
- **Framework**: Flutter (Web & Mobile)
- **State Management**: Riverpod
- **HTTP Client**: Dio
- **UI Components**: Material Design 3

#### DevOps & Infrastructure
- **Containerization**: Docker
- **Orchestration**: Kubernetes
- **CI/CD**: GitHub Actions
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)

## Key Features

### Core E-commerce Features
- Multi-store marketplace
- Product variants (color, size, images, stock)
- Shopping cart and wishlist
- Order management and tracking
- Payment processing with multiple gateways
- Inventory management with real-time tracking
- Commission tracking for sellers

### Advanced Features
- Personalized recommendations
- Multi-language and multi-currency support
- Real-time notifications
- Advanced analytics and reporting
- Bulk product management
- Return and refund management
- Live chat and support ticketing

### User Roles
- **Customers**: Browse, purchase, review products
- **Sellers**: Manage stores, products, view analytics
- **Admins**: Platform management, user oversight, system analytics

## Project Structure
```
marcat/
├── services/                 # Microservices
│   ├── api-gateway/         # API Gateway service
│   ├── user-service/        # User management
│   ├── store-service/       # Store management
│   ├── product-service/     # Product catalog
│   ├── order-service/       # Order processing
│   ├── payment-service/     # Payment handling
│   ├── inventory-service/   # Inventory management
│   ├── notification-service/# Notifications
│   ├── analytics-service/   # Analytics and reporting
│   ├── review-service/      # Reviews and ratings
│   └── support-service/     # Customer support
├── frontend/                # Flutter application
│   ├── web/                # Web-specific code
│   ├── mobile/             # Mobile-specific code
│   └── shared/             # Shared components and logic
├── shared/                  # Shared libraries and utilities
│   ├── types/              # TypeScript type definitions
│   ├── utils/              # Common utilities
│   └── constants/          # Shared constants
├── infrastructure/          # Infrastructure as Code
│   ├── docker/             # Docker configurations
│   ├── kubernetes/         # K8s manifests
│   └── terraform/          # Infrastructure provisioning
├── docs/                   # Documentation
│   ├── api/               # API documentation
│   ├── architecture/      # System architecture docs
│   └── deployment/        # Deployment guides
└── scripts/               # Build and deployment scripts
```

## Getting Started

### Prerequisites
- Node.js 18+
- Flutter 3.0+
- Docker & Docker Compose
- PostgreSQL 14+
- Redis 6+

### Development Setup
1. Clone the repository
2. Install dependencies for each service
3. Set up environment variables
4. Run database migrations
5. Start services with Docker Compose

### Environment Configuration
Each service requires specific environment variables for database connections, API keys, and service configurations. See individual service README files for detailed setup instructions.

## API Documentation
Comprehensive API documentation is available at `/docs` endpoint when running the API Gateway in development mode.

## Contributing
Please read our contributing guidelines and code of conduct before submitting pull requests.

## License
This project is licensed under the MIT License - see the LICENSE file for details.