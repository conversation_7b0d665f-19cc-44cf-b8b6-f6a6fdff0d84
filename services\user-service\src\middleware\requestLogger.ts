import morgan from 'morgan';
import { Request, Response } from 'express';
import { logger, loggerStream } from '../utils/logger';
import { config } from '../config/config';

// Custom token for user ID
morgan.token('user-id', (req: Request) => {
  return (req as any).user?.id || 'anonymous';
});

// Custom token for request ID
morgan.token('request-id', (req: Request) => {
  return req.headers['x-request-id'] as string || 'unknown';
});

// Custom token for response time in milliseconds
morgan.token('response-time-ms', (req: Request, res: Response) => {
  const startTime = (req as any).startTime;
  if (!startTime) return '0';
  return `${Date.now() - startTime}ms`;
});

// Custom token for request body size
morgan.token('req-size', (req: Request) => {
  const contentLength = req.headers['content-length'];
  return contentLength ? `${contentLength}B` : '0B';
});

// Custom token for response body size
morgan.token('res-size', (req: Request, res: Response) => {
  const contentLength = res.getHeader('content-length');
  return contentLength ? `${contentLength}B` : '0B';
});

// Custom token for user agent (truncated)
morgan.token('user-agent-short', (req: Request) => {
  const userAgent = req.headers['user-agent'];
  return userAgent ? userAgent.substring(0, 50) : 'unknown';
});

// Development format - detailed and colorized
const developmentFormat = [
  ':method',
  ':url',
  ':status',
  ':response-time ms',
  '- :res[content-length]',
  'User: :user-id',
  'IP: :remote-addr',
].join(' ');

// Production format - structured and comprehensive
const productionFormat = JSON.stringify({
  method: ':method',
  url: ':url',
  status: ':status',
  responseTime: ':response-time',
  contentLength: ':res[content-length]',
  userAgent: ':user-agent-short',
  userId: ':user-id',
  requestId: ':request-id',
  ip: ':remote-addr',
  timestamp: ':date[iso]',
});

// Skip function for health checks and static assets
const skipFunction = (req: Request, res: Response): boolean => {
  // Skip health check endpoints
  if (req.url?.startsWith('/health')) {
    return true;
  }

  // Skip static assets in production
  if (config.nodeEnv === 'production' && req.url?.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/)) {
    return true;
  }

  // Skip successful requests in production (only log errors and warnings)
  if (config.nodeEnv === 'production' && res.statusCode < 400) {
    return true;
  }

  return false;
};

// Request timing middleware
export const requestTimer = (req: Request, res: Response, next: Function): void => {
  (req as any).startTime = Date.now();
  next();
};

// Main request logger
export const requestLogger = morgan(
  config.nodeEnv === 'development' ? developmentFormat : productionFormat,
  {
    stream: loggerStream,
    skip: skipFunction,
  }
);

// Detailed request logger for debugging
export const detailedRequestLogger = (req: Request, res: Response, next: Function): void => {
  const startTime = Date.now();
  const originalSend = res.send;

  // Override res.send to capture response
  res.send = function (body: any) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Log detailed request information
    logger.debug('Detailed Request Log', {
      request: {
        method: req.method,
        url: req.url,
        path: req.path,
        query: req.query,
        params: req.params,
        headers: {
          'content-type': req.headers['content-type'],
          'user-agent': req.headers['user-agent'],
          'authorization': req.headers.authorization ? '[REDACTED]' : undefined,
          'x-forwarded-for': req.headers['x-forwarded-for'],
          'x-real-ip': req.headers['x-real-ip'],
        },
        body: req.method !== 'GET' ? sanitizeRequestBody(req.body) : undefined,
        ip: req.ip,
        userId: (req as any).user?.id,
      },
      response: {
        statusCode: res.statusCode,
        headers: {
          'content-type': res.getHeader('content-type'),
          'content-length': res.getHeader('content-length'),
        },
        body: config.nodeEnv === 'development' ? sanitizeResponseBody(body) : undefined,
      },
      timing: {
        duration,
        timestamp: new Date().toISOString(),
      },
    });

    return originalSend.call(this, body);
  };

  next();
};

// Security-focused request logger
export const securityRequestLogger = (req: Request, res: Response, next: Function): void => {
  // Log potentially suspicious requests
  const suspiciousPatterns = [
    /\.\./,  // Path traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /data:.*base64/i,  // Data URI attacks
  ];

  const isSuspicious = suspiciousPatterns.some(pattern =>
    pattern.test(req.url || '') ||
    pattern.test(JSON.stringify(req.body || {})) ||
    pattern.test(JSON.stringify(req.query || {}))
  );

  if (isSuspicious) {
    logger.warn('Suspicious Request Detected', {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      body: sanitizeRequestBody(req.body),
      query: req.query,
      headers: {
        'x-forwarded-for': req.headers['x-forwarded-for'],
        'x-real-ip': req.headers['x-real-ip'],
        'referer': req.headers.referer,
      },
      timestamp: new Date().toISOString(),
    });
  }

  next();
};

// Helper function to sanitize request body for logging
const sanitizeRequestBody = (body: any): any => {
  if (!body) return body;

  const sanitized = { ...body };

  // Remove sensitive fields
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'credential'];

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
};

// Helper function to sanitize response body for logging
const sanitizeResponseBody = (body: any): any => {
  if (!body) return body;

  try {
    const parsed = typeof body === 'string' ? JSON.parse(body) : body;

    if (parsed && typeof parsed === 'object') {
      const sanitized = { ...parsed };

      // Remove sensitive fields from response
      const sensitiveFields = ['token', 'password', 'secret', 'key'];

      for (const field of sensitiveFields) {
        if (sanitized[field]) {
          sanitized[field] = '[REDACTED]';
        }
      }

      return sanitized;
    }
  } catch (error) {
    // If parsing fails, return truncated string
    return typeof body === 'string' ? body.substring(0, 200) + '...' : body;
  }

  return body;
};