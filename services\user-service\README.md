# User Service - Marcat Platform

The User Service is a comprehensive microservice responsible for user management, authentication, and authorization within the Marcat e-commerce platform.

## Features

### Authentication & Authorization
- **User Registration** with email verification
- **Secure Login** with JWT tokens and refresh tokens
- **Password Reset** functionality with secure tokens
- **Session Management** with device tracking and geolocation
- **Role-based Access Control** (<PERSON><PERSON>, Se<PERSON>, Admin, Super Admin)
- **Account Security** with failed login attempt tracking and account locking

### User Management
- **Profile Management** with comprehensive user information
- **Avatar Upload** and management
- **User Search** and filtering (admin only)
- **Account Suspension** and role management (admin only)
- **Activity Tracking** with risk assessment and audit logs

### Security Features
- **Rate Limiting** with Redis-backed distributed limiting
- **Input Validation** with comprehensive Joi schemas
- **Password Security** with bcrypt hashing and strong password requirements
- **Token Blacklisting** for secure logout
- **Activity Monitoring** with suspicious activity detection
- **CORS Protection** and security headers

## Technology Stack

- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL with Knex.js ORM
- **Cache**: Redis for sessions, rate limiting, and caching
- **Authentication**: JWT with refresh tokens
- **Email**: Nodemailer for transactional emails
- **Testing**: Jest with Supertest
- **Validation**: Joi for input validation
- **Logging**: Winston for structured logging

## API Endpoints

### Authentication Routes (`/api/auth`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/register` | Register new user | No |
| POST | `/login` | User login | No |
| POST | `/logout` | User logout | Yes |
| POST | `/logout-all` | Logout from all devices | Yes |
| POST | `/refresh-token` | Refresh access token | No |
| GET | `/verify-email/:token` | Verify email address | No |
| POST | `/request-password-reset` | Request password reset | No |
| POST | `/reset-password` | Reset password with token | No |
| POST | `/resend-verification` | Resend verification email | No |
| GET | `/me` | Get current user info | Yes |

### User Routes (`/api/users`)

| Method | Endpoint | Description | Auth Required | Role Required |
|--------|----------|-------------|---------------|---------------|
| GET | `/profile` | Get user profile | Yes | Any |
| PUT | `/profile` | Update user profile | Yes | Any |
| POST | `/change-password` | Change password | Yes | Any |
| PUT | `/avatar` | Update user avatar | Yes | Any |
| GET | `/sessions` | Get user sessions | Yes | Any |
| DELETE | `/sessions/:id` | Revoke user session | Yes | Any |
| GET | `/activity` | Get user activity | Yes | Any |
| GET | `/stats` | Get user statistics | Yes | Any |
| GET | `/:id` | Get user by ID | Yes | Any |
| GET | `/` | Search users | Yes | Admin |
| PUT | `/:id/role` | Update user role | Yes | Super Admin |
| POST | `/:id/suspend` | Suspend user | Yes | Admin |
| DELETE | `/account` | Delete account | Yes | Any |

### Health Check (`/health`)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Service health check |
| GET | `/ready` | Readiness probe |
| GET | `/live` | Liveness probe |

## Database Schema

### Users Table
- **id**: UUID primary key
- **email**: Unique email address
- **password_hash**: Bcrypt hashed password
- **first_name**: User's first name
- **last_name**: User's last name
- **phone_number**: Optional phone number
- **date_of_birth**: Optional date of birth
- **bio**: Optional user biography
- **role**: User role (customer, seller, admin, super_admin)
- **status**: Account status (active, inactive, suspended, deleted)
- **avatar_url**: Profile picture URL
- **cover_image_url**: Cover image URL
- **preferences**: JSON preferences object
- **address**: JSON address object
- **email_verified**: Email verification status
- **email_verification_token**: Email verification token
- **two_factor_enabled**: 2FA status
- **failed_login_attempts**: Failed login counter
- **locked_until**: Account lock expiration
- **last_login_at**: Last login timestamp
- **created_at**: Account creation timestamp
- **updated_at**: Last update timestamp
- **deleted_at**: Soft delete timestamp

### User Sessions Table
- **id**: UUID primary key
- **user_id**: Foreign key to users table
- **token_hash**: Hashed session token
- **refresh_token_hash**: Hashed refresh token
- **device_type**: Device type (mobile, tablet, desktop)
- **device_name**: Device name/browser
- **ip_address**: Client IP address
- **user_agent**: Client user agent
- **country**: Geolocation country
- **city**: Geolocation city
- **expires_at**: Token expiration
- **refresh_expires_at**: Refresh token expiration
- **last_activity_at**: Last activity timestamp
- **revoked**: Revocation status
- **revoked_at**: Revocation timestamp
- **revoked_reason**: Revocation reason
- **created_at**: Session creation timestamp

### User Activities Table
- **id**: UUID primary key
- **user_id**: Foreign key to users table
- **activity_type**: Type of activity
- **description**: Activity description
- **ip_address**: Client IP address
- **user_agent**: Client user agent
- **country**: Geolocation country
- **city**: Geolocation city
- **risk_level**: Risk assessment (low, medium, high)
- **requires_review**: Admin review flag
- **reviewed_by**: Admin who reviewed
- **reviewed_at**: Review timestamp
- **metadata**: JSON metadata object
- **created_at**: Activity timestamp

### Password Resets Table
- **id**: UUID primary key
- **user_id**: Foreign key to users table
- **token_hash**: Hashed reset token
- **expires_at**: Token expiration
- **used**: Usage status
- **used_at**: Usage timestamp
- **ip_address**: Request IP address
- **user_agent**: Request user agent
- **attempts**: Usage attempts counter
- **created_at**: Request timestamp

## Environment Variables

```env
# Server Configuration
NODE_ENV=development
PORT=3001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=marcat_user_service
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSL=false
DB_POOL_MIN=2
DB_POOL_MAX=10

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Security Configuration
BCRYPT_SALT_ROUNDS=12
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined
```

## Installation & Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Set Environment Variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run Database Migrations**
   ```bash
   npm run migrate:up
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

## Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Deployment

### Docker Deployment

```bash
# Build image
docker build -t marcat/user-service .

# Run container
docker run -p 3001:3001 --env-file .env marcat/user-service
```

### Kubernetes Deployment

```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/
```

## Monitoring & Logging

- **Health Checks**: Available at `/health`, `/health/ready`, `/health/live`
- **Metrics**: Prometheus metrics available at `/metrics` (if enabled)
- **Logging**: Structured JSON logs with Winston
- **Error Tracking**: Comprehensive error handling and logging

## Security Considerations

- All passwords are hashed with bcrypt (12 salt rounds)
- JWT tokens have short expiration times (15 minutes)
- Refresh tokens are rotated on each use
- Rate limiting prevents brute force attacks
- Input validation prevents injection attacks
- CORS protection prevents unauthorized cross-origin requests
- Security headers protect against common vulnerabilities
- Activity logging provides audit trails
- Failed login attempts trigger account locking

## Contributing

1. Follow TypeScript best practices
2. Write comprehensive tests for new features
3. Update documentation for API changes
4. Follow the existing code style and patterns
5. Ensure all tests pass before submitting PRs

## License

This project is proprietary software for the Marcat platform.
