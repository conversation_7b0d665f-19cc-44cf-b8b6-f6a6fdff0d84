import { Request, Response, NextFunction } from 'express';
import { UserService } from '../services/UserService';
import { UserRole, UserStatus, UserActivityType } from '@marcat/shared-types';
import { logger } from '../utils/logger';
import { ValidationError, AuthorizationError } from '../middleware/errorHandler';

export class UserController {
  // Get current user profile
  static async getProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      const user = await UserService.getUserById(userId);

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phoneNumber: user.phoneNumber,
            dateOfBirth: user.dateOfBirth,
            bio: user.bio,
            role: user.role,
            status: user.status,
            avatarUrl: user.avatarUrl,
            coverImageUrl: user.coverImageUrl,
            preferences: user.preferences,
            address: user.address,
            emailVerified: user.emailVerified,
            twoFactorEnabled: user.twoFactorEnabled,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get user by ID (public profile or full profile for admins)
  static async getUserById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const requestingUserId = req.user?.id;

      const user = await UserService.getUserById(id, requestingUserId);

      res.json({
        success: true,
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // Update user profile
  static async updateProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const updateData = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      const updatedUser = await UserService.updateProfile(
        userId,
        updateData,
        ipAddress,
        userAgent
      );

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  }

  // Change password
  static async changePassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { currentPassword, newPassword } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      if (!currentPassword || !newPassword) {
        throw new ValidationError('Current password and new password are required');
      }

      const result = await UserService.changePassword(
        userId,
        currentPassword,
        newPassword,
        ipAddress,
        userAgent
      );

      res.json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update user avatar
  static async updateAvatar(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { avatarUrl } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      if (!avatarUrl) {
        throw new ValidationError('Avatar URL is required');
      }

      const updatedUser = await UserService.updateAvatar(
        userId,
        avatarUrl,
        ipAddress,
        userAgent
      );

      res.json({
        success: true,
        message: 'Avatar updated successfully',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get user sessions
  static async getSessions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      const sessions = await UserService.getUserSessions(userId);

      res.json({
        success: true,
        data: { sessions },
      });
    } catch (error) {
      next(error);
    }
  }

  // Revoke user session
  static async revokeSession(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      const result = await UserService.revokeSession(
        userId,
        sessionId,
        ipAddress,
        userAgent
      );

      res.json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get user activity history
  static async getActivity(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { page, limit, activityType, startDate, endDate } = req.query;

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      const options = {
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined,
        activityType: activityType as UserActivityType,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
      };

      const result = await UserService.getUserActivity(userId, options);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get user statistics
  static async getStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      const stats = await UserService.getUserStats(userId);

      res.json({
        success: true,
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  }

  // Search users (admin only)
  static async searchUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const requestingUserId = req.user?.id;
      const { page, limit, sortBy, sortOrder, status, role, search } = req.query;

      if (!requestingUserId) {
        throw new ValidationError('User ID is required');
      }

      const searchOptions = {
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined,
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        status: status as UserStatus,
        role: role as UserRole,
        search: search as string,
      };

      const result = await UserService.searchUsers(searchOptions, requestingUserId);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update user role (super admin only)
  static async updateUserRole(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const requestingUserId = req.user?.id;
      const { id } = req.params;
      const { role } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      if (!requestingUserId) {
        throw new ValidationError('User ID is required');
      }

      if (!role || !Object.values(UserRole).includes(role)) {
        throw new ValidationError('Valid role is required');
      }

      const updatedUser = await UserService.updateUserRole(
        id,
        role,
        requestingUserId,
        ipAddress,
        userAgent
      );

      res.json({
        success: true,
        message: 'User role updated successfully',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  }

  // Suspend user (admin only)
  static async suspendUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const requestingUserId = req.user?.id;
      const { id } = req.params;
      const { reason } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      if (!requestingUserId) {
        throw new ValidationError('User ID is required');
      }

      if (!reason) {
        throw new ValidationError('Suspension reason is required');
      }

      const updatedUser = await UserService.suspendUser(
        id,
        reason,
        requestingUserId,
        ipAddress,
        userAgent
      );

      res.json({
        success: true,
        message: 'User suspended successfully',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete user account (soft delete)
  static async deleteAccount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { password } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      if (!password) {
        throw new ValidationError('Password confirmation is required');
      }

      // TODO: Implement account deletion with password verification
      // const result = await UserService.deleteAccount(userId, password, ipAddress, userAgent);

      res.json({
        success: true,
        message: 'Account deletion request submitted. Your account will be deleted within 30 days.',
      });
    } catch (error) {
      next(error);
    }
  }
}
