import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { validate, schemas } from '../middleware/validation';
import { authenticate, requireRole, requireOwnership } from '../middleware/auth';
import { rateLimiters } from '../middleware/rateLimiter';
import { UserRole } from '@marcat/shared-types';

const router = Router();

// All user routes require authentication
router.use(authenticate);

// User profile routes
router.get(
  '/profile',
  rateLimiters.general,
  UserController.getProfile
);

router.put(
  '/profile',
  rateLimiters.general,
  validate(schemas.userProfileUpdate),
  UserController.updateProfile
);

router.post(
  '/change-password',
  rateLimiters.auth,
  UserController.changePassword
);

router.put(
  '/avatar',
  rateLimiters.general,
  UserController.updateAvatar
);

// User session management
router.get(
  '/sessions',
  rateLimiters.general,
  UserController.getSessions
);

router.delete(
  '/sessions/:sessionId',
  rateLimiters.general,
  validate(schemas.uuid, 'params'),
  UserController.revokeSession
);

// User activity and statistics
router.get(
  '/activity',
  rateLimiters.general,
  validate(schemas.pagination, 'query'),
  UserController.getActivity
);

router.get(
  '/stats',
  rateLimiters.general,
  UserController.getStats
);

// Public user profile (can be viewed by anyone)
router.get(
  '/:id',
  rateLimiters.general,
  validate(schemas.uuid, 'params'),
  UserController.getUserById
);

// Admin routes
router.get(
  '/',
  rateLimiters.general,
  requireRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  validate(schemas.pagination, 'query'),
  UserController.searchUsers
);

router.put(
  '/:id/role',
  rateLimiters.admin,
  requireRole([UserRole.SUPER_ADMIN]),
  validate(schemas.uuid, 'params'),
  UserController.updateUserRole
);

router.post(
  '/:id/suspend',
  rateLimiters.admin,
  requireRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  validate(schemas.uuid, 'params'),
  UserController.suspendUser
);

// Account deletion
router.delete(
  '/account',
  rateLimiters.auth,
  UserController.deleteAccount
);

export default router;
