import { db } from '../config/database';
import { UserSession } from '@marcat/shared-types';
import { logger } from '../utils/logger';

export class UserSessionModel {
  private static tableName = 'user_sessions';

  // Create a new session
  static async create(sessionData: Partial<UserSession>): Promise<UserSession> {
    try {
      const [session] = await db(this.tableName)
        .insert({
          ...sessionData,
          created_at: new Date(),
          updated_at: new Date(),
        })
        .returning('*');
      
      logger.info('User session created', { 
        sessionId: session.id, 
        userId: session.user_id,
        deviceType: session.device_type 
      });
      
      return this.mapDbSessionToSession(session);
    } catch (error) {
      logger.error('Error creating user session:', error);
      throw error;
    }
  }

  // Find session by token
  static async findByToken(sessionToken: string): Promise<UserSession | null> {
    try {
      const session = await db(this.tableName)
        .where({ session_token: sessionToken, is_active: true })
        .where('expires_at', '>', new Date())
        .first();
      
      return session ? this.mapDbSessionToSession(session) : null;
    } catch (error) {
      logger.error('Error finding session by token:', error);
      throw error;
    }
  }

  // Find session by refresh token
  static async findByRefreshToken(refreshToken: string): Promise<UserSession | null> {
    try {
      const session = await db(this.tableName)
        .where({ refresh_token: refreshToken, is_active: true })
        .where('refresh_expires_at', '>', new Date())
        .first();
      
      return session ? this.mapDbSessionToSession(session) : null;
    } catch (error) {
      logger.error('Error finding session by refresh token:', error);
      throw error;
    }
  }

  // Find sessions by user ID
  static async findByUserId(userId: string, activeOnly: boolean = true): Promise<UserSession[]> {
    try {
      let query = db(this.tableName).where({ user_id: userId });
      
      if (activeOnly) {
        query = query.where({ is_active: true }).where('expires_at', '>', new Date());
      }
      
      const sessions = await query.orderBy('last_activity_at', 'desc');
      
      return sessions.map(session => this.mapDbSessionToSession(session));
    } catch (error) {
      logger.error('Error finding sessions by user ID:', error);
      throw error;
    }
  }

  // Update session activity
  static async updateActivity(sessionId: string): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ id: sessionId, is_active: true })
        .update({
          last_activity_at: new Date(),
          updated_at: new Date(),
        });
      
      return result > 0;
    } catch (error) {
      logger.error('Error updating session activity:', error);
      throw error;
    }
  }

  // Revoke session
  static async revoke(sessionId: string, reason: string = 'logout'): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ id: sessionId })
        .update({
          is_active: false,
          revoked_at: new Date(),
          revoked_reason: reason,
          updated_at: new Date(),
        });
      
      if (result > 0) {
        logger.info('Session revoked', { sessionId, reason });
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Error revoking session:', error);
      throw error;
    }
  }

  // Revoke all sessions for a user
  static async revokeAllForUser(userId: string, reason: string = 'security'): Promise<number> {
    try {
      const result = await db(this.tableName)
        .where({ user_id: userId, is_active: true })
        .update({
          is_active: false,
          revoked_at: new Date(),
          revoked_reason: reason,
          updated_at: new Date(),
        });
      
      if (result > 0) {
        logger.info('All sessions revoked for user', { userId, count: result, reason });
      }
      
      return result;
    } catch (error) {
      logger.error('Error revoking all sessions for user:', error);
      throw error;
    }
  }

  // Revoke session by token
  static async revokeByToken(sessionToken: string, reason: string = 'logout'): Promise<boolean> {
    try {
      const result = await db(this.tableName)
        .where({ session_token: sessionToken })
        .update({
          is_active: false,
          revoked_at: new Date(),
          revoked_reason: reason,
          updated_at: new Date(),
        });
      
      return result > 0;
    } catch (error) {
      logger.error('Error revoking session by token:', error);
      throw error;
    }
  }

  // Clean up expired sessions
  static async cleanupExpired(): Promise<number> {
    try {
      const result = await db(this.tableName)
        .where('expires_at', '<', new Date())
        .orWhere('refresh_expires_at', '<', new Date())
        .update({
          is_active: false,
          revoked_at: new Date(),
          revoked_reason: 'expired',
          updated_at: new Date(),
        });
      
      if (result > 0) {
        logger.info('Expired sessions cleaned up', { count: result });
      }
      
      return result;
    } catch (error) {
      logger.error('Error cleaning up expired sessions:', error);
      throw error;
    }
  }

  // Get session statistics for a user
  static async getSessionStats(userId: string): Promise<{
    totalSessions: number;
    activeSessions: number;
    deviceTypes: { [key: string]: number };
    recentActivity: Date | null;
  }> {
    try {
      // Total sessions
      const [{ count: totalSessions }] = await db(this.tableName)
        .where({ user_id: userId })
        .count('* as count');
      
      // Active sessions
      const [{ count: activeSessions }] = await db(this.tableName)
        .where({ user_id: userId, is_active: true })
        .where('expires_at', '>', new Date())
        .count('* as count');
      
      // Device types
      const deviceTypeResults = await db(this.tableName)
        .where({ user_id: userId })
        .select('device_type')
        .count('* as count')
        .groupBy('device_type');
      
      const deviceTypes: { [key: string]: number } = {};
      deviceTypeResults.forEach(result => {
        if (result.device_type) {
          deviceTypes[result.device_type] = parseInt(result.count as string);
        }
      });
      
      // Recent activity
      const recentSession = await db(this.tableName)
        .where({ user_id: userId })
        .orderBy('last_activity_at', 'desc')
        .first();
      
      return {
        totalSessions: parseInt(totalSessions as string),
        activeSessions: parseInt(activeSessions as string),
        deviceTypes,
        recentActivity: recentSession?.last_activity_at || null,
      };
    } catch (error) {
      logger.error('Error getting session stats:', error);
      throw error;
    }
  }

  // Map database session to UserSession interface
  private static mapDbSessionToSession(dbSession: any): UserSession {
    return {
      id: dbSession.id,
      userId: dbSession.user_id,
      sessionToken: dbSession.session_token,
      refreshToken: dbSession.refresh_token,
      expiresAt: dbSession.expires_at,
      refreshExpiresAt: dbSession.refresh_expires_at,
      deviceType: dbSession.device_type,
      deviceName: dbSession.device_name,
      userAgent: dbSession.user_agent,
      ipAddress: dbSession.ip_address,
      country: dbSession.country,
      city: dbSession.city,
      isActive: dbSession.is_active,
      lastActivityAt: dbSession.last_activity_at,
      revokedAt: dbSession.revoked_at,
      revokedReason: dbSession.revoked_reason,
      createdAt: dbSession.created_at,
      updatedAt: dbSession.updated_at,
    };
  }
}
