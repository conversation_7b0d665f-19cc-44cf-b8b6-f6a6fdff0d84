import { createClient, RedisClientType } from 'redis';
import { config } from './config';
import { logger } from '../utils/logger';

let redisClient: RedisClientType | null = null;
let redisSubscriber: RedisClientType | null = null;
let redisPublisher: RedisClientType | null = null;

export const connectRedis = async (): Promise<RedisClientType> => {
  try {
    if (redisClient) {
      return redisClient;
    }

    // Main Redis client
    redisClient = createClient({
      url: config.redis.url,
      password: config.redis.password,
      database: config.redis.db,
      socket: {
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            logger.error('Redis reconnection failed after 10 attempts');
            return new Error('Redis reconnection failed');
          }
          return Math.min(retries * 50, 1000);
        },
      },
    });

    // Subscriber client for pub/sub
    redisSubscriber = createClient({
      url: config.redis.url,
      password: config.redis.password,
      database: config.redis.db,
    });

    // Publisher client for pub/sub
    redisPublisher = createClient({
      url: config.redis.url,
      password: config.redis.password,
      database: config.redis.db,
    });

    // Error handlers
    redisClient.on('error', (err) => {
      logger.error('Redis client error:', err);
    });

    redisSubscriber.on('error', (err) => {
      logger.error('Redis subscriber error:', err);
    });

    redisPublisher.on('error', (err) => {
      logger.error('Redis publisher error:', err);
    });

    // Connection handlers
    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });

    redisClient.on('ready', () => {
      logger.info('Redis client ready');
    });

    redisClient.on('end', () => {
      logger.info('Redis client disconnected');
    });

    // Connect all clients
    await Promise.all([
      redisClient.connect(),
      redisSubscriber.connect(),
      redisPublisher.connect(),
    ]);

    logger.info('Redis connections established successfully');
    return redisClient;
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    throw error;
  }
};

export const getRedisClient = (): RedisClientType => {
  if (!redisClient) {
    throw new Error('Redis not initialized. Call connectRedis() first.');
  }
  return redisClient;
};

export const getRedisSubscriber = (): RedisClientType => {
  if (!redisSubscriber) {
    throw new Error('Redis subscriber not initialized. Call connectRedis() first.');
  }
  return redisSubscriber;
};

export const getRedisPublisher = (): RedisClientType => {
  if (!redisPublisher) {
    throw new Error('Redis publisher not initialized. Call connectRedis() first.');
  }
  return redisPublisher;
};

export const closeRedis = async (): Promise<void> => {
  try {
    const promises = [];

    if (redisClient) {
      promises.push(redisClient.quit());
    }

    if (redisSubscriber) {
      promises.push(redisSubscriber.quit());
    }

    if (redisPublisher) {
      promises.push(redisPublisher.quit());
    }

    await Promise.all(promises);

    redisClient = null;
    redisSubscriber = null;
    redisPublisher = null;

    logger.info('Redis connections closed');
  } catch (error) {
    logger.error('Error closing Redis connections:', error);
  }
};

// Health check function
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    if (!redisClient) {
      return false;
    }
    await redisClient.ping();
    return true;
  } catch (error) {
    logger.error('Redis health check failed:', error);
    return false;
  }
};

// Cache helper functions
export const setCache = async (key: string, value: any, ttl?: number): Promise<void> => {
  try {
    const client = getRedisClient();
    const serializedValue = JSON.stringify(value);

    if (ttl) {
      await client.setEx(key, ttl, serializedValue);
    } else {
      await client.set(key, serializedValue);
    }
  } catch (error) {
    logger.error('Error setting cache:', error);
    throw error;
  }
};

export const getCache = async <T>(key: string): Promise<T | null> => {
  try {
    const client = getRedisClient();
    const value = await client.get(key);

    if (!value) {
      return null;
    }

    return JSON.parse(value) as T;
  } catch (error) {
    logger.error('Error getting cache:', error);
    return null;
  }
};

export const deleteCache = async (key: string): Promise<void> => {
  try {
    const client = getRedisClient();
    await client.del(key);
  } catch (error) {
    logger.error('Error deleting cache:', error);
    throw error;
  }
};

export const deleteCachePattern = async (pattern: string): Promise<void> => {
  try {
    const client = getRedisClient();
    const keys = await client.keys(pattern);

    if (keys.length > 0) {
      await client.del(keys);
    }
  } catch (error) {
    logger.error('Error deleting cache pattern:', error);
    throw error;
  }
};