/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('user_activities', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to users table
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Activity information
    table.string('activity_type', 100).notNullable(); // login, logout, profile_update, password_change, etc.
    table.string('description', 500).notNullable();
    table.json('metadata').nullable(); // Additional activity-specific data
    
    // Request information
    table.string('ip_address', 45).notNullable();
    table.string('user_agent', 500).nullable();
    table.string('country', 100).nullable();
    table.string('city', 100).nullable();
    
    // Security and risk assessment
    table.enum('risk_level', ['LOW', 'MEDIUM', 'HIGH']).notNullable().defaultTo('LOW');
    table.boolean('requires_review').notNullable().defaultTo(false);
    table.timestamp('reviewed_at').nullable();
    table.uuid('reviewed_by').nullable(); // Admin user who reviewed
    
    // Timestamps
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    
    // Indexes
    table.index(['user_id']);
    table.index(['activity_type']);
    table.index(['created_at']);
    table.index(['risk_level']);
    table.index(['requires_review']);
    table.index(['ip_address']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('user_activities');
};
