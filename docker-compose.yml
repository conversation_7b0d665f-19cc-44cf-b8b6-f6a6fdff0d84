version: '3.8'

services:
  # Infrastructure Services
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: marcat_dev
      POSTGRES_USER: marcat_user
      POSTGRES_PASSWORD: marcat_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - marcat-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - marcat-network

  # API Gateway
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - REDIS_URL=redis://redis:6379
      - USER_SERVICE_URL=http://user-service:3001
      - STORE_SERVICE_URL=http://store-service:3002
      - PRODUCT_SERVICE_URL=http://product-service:3003
      - ORDER_SERVICE_URL=http://order-service:3004
      - PAYMENT_SERVICE_URL=http://payment-service:3005
      - INVENTORY_SERVICE_URL=http://inventory-service:3006
      - NOTIFICATION_SERVICE_URL=http://notification-service:3007
      - ANALYTICS_SERVICE_URL=http://analytics-service:3008
      - REVIEW_SERVICE_URL=http://review-service:3009
      - SUPPORT_SERVICE_URL=http://support-service:3010
    depends_on:
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/api-gateway:/app
      - /app/node_modules

  # Microservices
  user-service:
    build:
      context: ./services/user-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=******************************************************/marcat_users
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - JWT_REFRESH_SECRET=your-jwt-refresh-secret-key
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/user-service:/app
      - /app/node_modules

  store-service:
    build:
      context: ./services/store-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - DATABASE_URL=******************************************************/marcat_stores
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/store-service:/app
      - /app/node_modules

  product-service:
    build:
      context: ./services/product-service
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - DATABASE_URL=******************************************************/marcat_products
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/product-service:/app
      - /app/node_modules

  order-service:
    build:
      context: ./services/order-service
      dockerfile: Dockerfile
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - DATABASE_URL=******************************************************/marcat_orders
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/order-service:/app
      - /app/node_modules

  payment-service:
    build:
      context: ./services/payment-service
      dockerfile: Dockerfile
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - DATABASE_URL=******************************************************/marcat_payments
      - REDIS_URL=redis://redis:6379
      - STRIPE_SECRET_KEY=your-stripe-secret-key
      - PAYPAL_CLIENT_ID=your-paypal-client-id
      - PAYPAL_CLIENT_SECRET=your-paypal-client-secret
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/payment-service:/app
      - /app/node_modules

  inventory-service:
    build:
      context: ./services/inventory-service
      dockerfile: Dockerfile
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - DATABASE_URL=******************************************************/marcat_inventory
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/inventory-service:/app
      - /app/node_modules

  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=development
      - PORT=3007
      - REDIS_URL=redis://redis:6379
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USER=<EMAIL>
      - SMTP_PASS=your-email-password
      - TWILIO_ACCOUNT_SID=your-twilio-account-sid
      - TWILIO_AUTH_TOKEN=your-twilio-auth-token
    depends_on:
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/notification-service:/app
      - /app/node_modules

  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: Dockerfile
    ports:
      - "3008:3008"
    environment:
      - NODE_ENV=development
      - PORT=3008
      - DATABASE_URL=******************************************************/marcat_analytics
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/analytics-service:/app
      - /app/node_modules

  review-service:
    build:
      context: ./services/review-service
      dockerfile: Dockerfile
    ports:
      - "3009:3009"
    environment:
      - NODE_ENV=development
      - PORT=3009
      - DATABASE_URL=******************************************************/marcat_reviews
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/review-service:/app
      - /app/node_modules

  support-service:
    build:
      context: ./services/support-service
      dockerfile: Dockerfile
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=development
      - PORT=3010
      - DATABASE_URL=******************************************************/marcat_support
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - marcat-network
    volumes:
      - ./services/support-service:/app
      - /app/node_modules

networks:
  marcat-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data: