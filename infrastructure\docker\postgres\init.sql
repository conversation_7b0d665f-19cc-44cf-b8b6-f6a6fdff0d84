-- Create databases for each microservice
CREATE DATABASE marcat_users;
CREATE DATABASE marcat_stores;
CREATE DATABASE marcat_products;
CREATE DATABASE marcat_orders;
CREATE DATABASE marcat_payments;
CREATE DATABASE marcat_inventory;
CREATE DATABASE marcat_analytics;
CREATE DATABASE marcat_reviews;
CREATE DATABASE marcat_support;

-- Grant permissions to marcat_user for all databases
GRANT ALL PRIVILEGES ON DATABASE marcat_users TO marcat_user;
GRANT ALL PRIVILEGES ON DATABASE marcat_stores TO marcat_user;
GRANT ALL PRIVILEGES ON DATABASE marcat_products TO marcat_user;
GRANT ALL PRIVILEGES ON DATABASE marcat_orders TO marcat_user;
GRANT ALL PRIVILEGES ON DATABASE marcat_payments TO marcat_user;
GRANT ALL PRIVILEGES ON DATABASE marcat_inventory TO marcat_user;
GRANT ALL PRIVILEGES ON DATABASE marcat_analytics TO marcat_user;
GRANT ALL PRIVILEGES ON DATABASE marcat_reviews TO marcat_user;
GRANT ALL PRIVILEGES ON DATABASE marcat_support TO marcat_user;