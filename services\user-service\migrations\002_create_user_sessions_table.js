/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('user_sessions', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to users table
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Session information
    table.string('session_token', 255).notNullable().unique();
    table.string('refresh_token', 255).notNullable().unique();
    table.timestamp('expires_at').notNullable();
    table.timestamp('refresh_expires_at').notNullable();
    
    // Device and location information
    table.string('device_type', 50).nullable(); // mobile, desktop, tablet
    table.string('device_name', 100).nullable(); // iPhone 12, Chrome Browser, etc.
    table.string('user_agent', 500).nullable();
    table.string('ip_address', 45).notNullable(); // IPv6 support
    table.string('country', 100).nullable();
    table.string('city', 100).nullable();
    
    // Session status
    table.boolean('is_active').notNullable().defaultTo(true);
    table.timestamp('last_activity_at').notNullable().defaultTo(knex.fn.now());
    table.timestamp('revoked_at').nullable();
    table.string('revoked_reason', 100).nullable(); // logout, security, expired, etc.
    
    // Timestamps
    table.timestamps(true, true); // created_at, updated_at
    
    // Indexes
    table.index(['user_id']);
    table.index(['session_token']);
    table.index(['refresh_token']);
    table.index(['expires_at']);
    table.index(['is_active']);
    table.index(['created_at']);
    table.index(['last_activity_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('user_sessions');
};
