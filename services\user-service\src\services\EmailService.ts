import nodemailer from 'nodemailer';
import { config } from '../config/config';
import { logger } from '../utils/logger';

export class EmailService {
  private static transporter: nodemailer.Transporter;

  // Initialize email transporter
  static async initialize(): Promise<void> {
    try {
      this.transporter = nodemailer.createTransporter({
        host: config.email.smtp.host,
        port: config.email.smtp.port,
        secure: config.email.smtp.secure, // true for 465, false for other ports
        auth: {
          user: config.email.smtp.auth.user,
          pass: config.email.smtp.auth.pass,
        },
      });

      // Verify connection configuration
      await this.transporter.verify();
      logger.info('Email service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize email service:', error);
      throw error;
    }
  }

  // Send verification email
  static async sendVerificationEmail(
    email: string,
    firstName: string,
    verificationToken: string
  ): Promise<void> {
    try {
      const verificationUrl = `http://localhost:3000/verify-email?token=${verificationToken}`;

      const mailOptions = {
        from: config.email.from,
        to: email,
        subject: 'Verify Your Email Address - Marcat',
        html: this.getVerificationEmailTemplate(firstName, verificationUrl),
        text: this.getVerificationEmailText(firstName, verificationUrl),
      };

      await this.transporter.sendMail(mailOptions);
      
      logger.info('Verification email sent successfully', { email });
    } catch (error) {
      logger.error('Failed to send verification email:', error);
      throw error;
    }
  }

  // Send password reset email
  static async sendPasswordResetEmail(
    email: string,
    firstName: string,
    resetToken: string
  ): Promise<void> {
    try {
      const resetUrl = `http://localhost:3000/reset-password?token=${resetToken}`;

      const mailOptions = {
        from: config.email.from,
        to: email,
        subject: 'Reset Your Password - Marcat',
        html: this.getPasswordResetEmailTemplate(firstName, resetUrl),
        text: this.getPasswordResetEmailText(firstName, resetUrl),
      };

      await this.transporter.sendMail(mailOptions);
      
      logger.info('Password reset email sent successfully', { email });
    } catch (error) {
      logger.error('Failed to send password reset email:', error);
      throw error;
    }
  }

  // Send welcome email
  static async sendWelcomeEmail(
    email: string,
    firstName: string
  ): Promise<void> {
    try {
      const mailOptions = {
        from: config.email.from,
        to: email,
        subject: 'Welcome to Marcat!',
        html: this.getWelcomeEmailTemplate(firstName),
        text: this.getWelcomeEmailText(firstName),
      };

      await this.transporter.sendMail(mailOptions);
      
      logger.info('Welcome email sent successfully', { email });
    } catch (error) {
      logger.error('Failed to send welcome email:', error);
      throw error;
    }
  }

  // Send account suspension notification
  static async sendSuspensionEmail(
    email: string,
    firstName: string,
    reason: string
  ): Promise<void> {
    try {
      const mailOptions = {
        from: config.email.from,
        to: email,
        subject: 'Account Suspended - Marcat',
        html: this.getSuspensionEmailTemplate(firstName, reason),
        text: this.getSuspensionEmailText(firstName, reason),
      };

      await this.transporter.sendMail(mailOptions);
      
      logger.info('Suspension email sent successfully', { email });
    } catch (error) {
      logger.error('Failed to send suspension email:', error);
      throw error;
    }
  }

  // Email verification template
  private static getVerificationEmailTemplate(firstName: string, verificationUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Verify Your Email</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to Marcat!</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName},</h2>
            <p>Thank you for signing up for Marcat! To complete your registration, please verify your email address by clicking the button below:</p>
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href="${verificationUrl}">${verificationUrl}</a></p>
            <p>This verification link will expire in 24 hours.</p>
            <p>If you didn't create an account with Marcat, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 Marcat. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Email verification text version
  private static getVerificationEmailText(firstName: string, verificationUrl: string): string {
    return `
      Hi ${firstName},

      Thank you for signing up for Marcat! To complete your registration, please verify your email address by visiting this link:

      ${verificationUrl}

      This verification link will expire in 24 hours.

      If you didn't create an account with Marcat, please ignore this email.

      Best regards,
      The Marcat Team
    `;
  }

  // Password reset email template
  private static getPasswordResetEmailTemplate(firstName: string, resetUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Reset Your Password</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Password Reset Request</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName},</h2>
            <p>We received a request to reset your password for your Marcat account. Click the button below to reset your password:</p>
            <a href="${resetUrl}" class="button">Reset Password</a>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href="${resetUrl}">${resetUrl}</a></p>
            <p>This password reset link will expire in 1 hour.</p>
            <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 Marcat. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Password reset text version
  private static getPasswordResetEmailText(firstName: string, resetUrl: string): string {
    return `
      Hi ${firstName},

      We received a request to reset your password for your Marcat account. Visit this link to reset your password:

      ${resetUrl}

      This password reset link will expire in 1 hour.

      If you didn't request a password reset, please ignore this email. Your password will remain unchanged.

      Best regards,
      The Marcat Team
    `;
  }

  // Welcome email template
  private static getWelcomeEmailTemplate(firstName: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Welcome to Marcat</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to Marcat!</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName},</h2>
            <p>Your email has been verified successfully! Welcome to Marcat, your one-stop destination for men's clothing.</p>
            <p>You can now:</p>
            <ul>
              <li>Browse thousands of products from top brands</li>
              <li>Create your personalized wishlist</li>
              <li>Track your orders in real-time</li>
              <li>Enjoy exclusive member discounts</li>
            </ul>
            <p>Start exploring our collection and find your perfect style!</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 Marcat. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Welcome email text version
  private static getWelcomeEmailText(firstName: string): string {
    return `
      Hi ${firstName},

      Your email has been verified successfully! Welcome to Marcat, your one-stop destination for men's clothing.

      You can now:
      - Browse thousands of products from top brands
      - Create your personalized wishlist
      - Track your orders in real-time
      - Enjoy exclusive member discounts

      Start exploring our collection and find your perfect style!

      Best regards,
      The Marcat Team
    `;
  }

  // Account suspension email template
  private static getSuspensionEmailTemplate(firstName: string, reason: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Account Suspended</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #ffc107; color: #212529; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Account Suspended</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName},</h2>
            <p>We're writing to inform you that your Marcat account has been temporarily suspended.</p>
            <p><strong>Reason:</strong> ${reason}</p>
            <p>If you believe this suspension was made in error or would like to appeal this decision, please contact our support team.</p>
            <p>We appreciate your understanding and look forward to resolving this matter.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 Marcat. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Account suspension text version
  private static getSuspensionEmailText(firstName: string, reason: string): string {
    return `
      Hi ${firstName},

      We're writing to inform you that your Marcat account has been temporarily suspended.

      Reason: ${reason}

      If you believe this suspension was made in error or would like to appeal this decision, please contact our support team.

      We appreciate your understanding and look forward to resolving this matter.

      Best regards,
      The Marcat Team
    `;
  }
}
