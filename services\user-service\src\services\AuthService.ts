import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { UserModel } from '../models/User';
import { UserSessionModel } from '../models/UserSession';
import { UserActivityModel } from '../models/UserActivity';
import { 
  User, 
  UserRole, 
  UserStatus, 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  UserActivityType,
  RiskLevel 
} from '@marcat/shared-types';
import { config } from '../config/config';
import { logger } from '../utils/logger';
import { AuthenticationError, ValidationError } from '../middleware/errorHandler';
import { setCache, getCache, deleteCache } from '../config/redis';

export class AuthService {
  private static readonly SALT_ROUNDS = 12;
  private static readonly MAX_LOGIN_ATTEMPTS = 5;
  private static readonly LOCK_DURATION = 15 * 60 * 1000; // 15 minutes
  private static readonly TOKEN_EXPIRY = '15m';
  private static readonly REFRESH_TOKEN_EXPIRY = '7d';

  // Register a new user
  static async register(
    registerData: RegisterRequest,
    ipAddress: string,
    userAgent?: string
  ): Promise<{ user: User; message: string }> {
    try {
      // Check if user already exists
      const existingUser = await UserModel.findByEmail(registerData.email);
      if (existingUser) {
        throw new ValidationError('User with this email already exists');
      }

      // Hash password
      const passwordHash = await bcrypt.hash(registerData.password, this.SALT_ROUNDS);

      // Generate email verification token
      const emailVerificationToken = crypto.randomBytes(32).toString('hex');
      const emailVerificationExpiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Create user
      const userData = {
        email: registerData.email.toLowerCase(),
        password_hash: passwordHash,
        first_name: registerData.firstName,
        last_name: registerData.lastName,
        phone_number: registerData.phoneNumber,
        date_of_birth: registerData.dateOfBirth,
        role: UserRole.CUSTOMER,
        status: UserStatus.PENDING_VERIFICATION,
        email_verification_token: emailVerificationToken,
        email_verification_expires_at: emailVerificationExpiresAt,
      };

      const user = await UserModel.create(userData);

      // Log registration activity
      await UserActivityModel.create({
        userId: user.id,
        activityType: UserActivityType.REGISTRATION,
        description: 'User registered successfully',
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.LOW,
      });

      logger.info('User registered successfully', {
        userId: user.id,
        email: user.email,
        ipAddress,
      });

      // TODO: Send verification email
      // await EmailService.sendVerificationEmail(user.email, emailVerificationToken);

      return {
        user,
        message: 'Registration successful. Please check your email to verify your account.',
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }

  // Login user
  static async login(
    loginData: LoginRequest,
    ipAddress: string,
    userAgent?: string,
    deviceInfo?: { deviceType?: string; deviceName?: string }
  ): Promise<LoginResponse> {
    try {
      // Find user by email
      const user = await UserModel.findByEmail(loginData.email);
      if (!user) {
        throw new AuthenticationError('Invalid email or password');
      }

      // Check if account is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        const lockTimeRemaining = Math.ceil((user.lockedUntil.getTime() - Date.now()) / 60000);
        throw new AuthenticationError(`Account is locked. Try again in ${lockTimeRemaining} minutes.`);
      }

      // Check if account is active
      if (user.status === UserStatus.SUSPENDED) {
        throw new AuthenticationError('Account is suspended. Please contact support.');
      }

      if (user.status === UserStatus.DELETED) {
        throw new AuthenticationError('Account not found.');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(loginData.password, user.password_hash);
      if (!isPasswordValid) {
        // Increment failed login attempts
        await UserModel.incrementFailedLoginAttempts(user.id);
        
        // Lock account if too many failed attempts
        if (user.failedLoginAttempts + 1 >= this.MAX_LOGIN_ATTEMPTS) {
          await UserModel.lockAccount(user.id, this.LOCK_DURATION);
          
          await UserActivityModel.create({
            userId: user.id,
            activityType: UserActivityType.ACCOUNT_LOCKED,
            description: 'Account locked due to too many failed login attempts',
            ipAddress,
            userAgent,
            riskLevel: RiskLevel.HIGH,
            requiresReview: true,
          });
          
          throw new AuthenticationError('Account has been locked due to too many failed login attempts.');
        }

        await UserActivityModel.create({
          userId: user.id,
          activityType: UserActivityType.LOGIN_FAILED,
          description: 'Failed login attempt - invalid password',
          ipAddress,
          userAgent,
          riskLevel: RiskLevel.MEDIUM,
        });

        throw new AuthenticationError('Invalid email or password');
      }

      // Generate tokens
      const { accessToken, refreshToken } = this.generateTokens(user);

      // Calculate token expiry dates
      const accessTokenExpiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
      const refreshTokenExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      // Create session
      const session = await UserSessionModel.create({
        user_id: user.id,
        session_token: accessToken,
        refresh_token: refreshToken,
        expires_at: accessTokenExpiresAt,
        refresh_expires_at: refreshTokenExpiresAt,
        device_type: deviceInfo?.deviceType,
        device_name: deviceInfo?.deviceName,
        user_agent: userAgent,
        ip_address: ipAddress,
        is_active: true,
        last_activity_at: new Date(),
      });

      // Update user login tracking
      await UserModel.updateLoginTracking(user.id, ipAddress);

      // Cache user data
      await setCache(`user:${user.id}`, user, 3600); // 1 hour

      // Log successful login
      await UserActivityModel.create({
        userId: user.id,
        activityType: UserActivityType.LOGIN,
        description: 'User logged in successfully',
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.LOW,
        metadata: {
          deviceType: deviceInfo?.deviceType,
          deviceName: deviceInfo?.deviceName,
        },
      });

      logger.info('User logged in successfully', {
        userId: user.id,
        email: user.email,
        ipAddress,
        deviceType: deviceInfo?.deviceType,
      });

      return {
        user,
        accessToken,
        refreshToken,
        expiresAt: accessTokenExpiresAt,
        refreshExpiresAt: refreshTokenExpiresAt,
        sessionId: session.id,
      };
    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }

  // Refresh access token
  static async refreshToken(
    refreshToken: string,
    ipAddress: string,
    userAgent?: string
  ): Promise<{ accessToken: string; expiresAt: Date }> {
    try {
      // Find session by refresh token
      const session = await UserSessionModel.findByRefreshToken(refreshToken);
      if (!session) {
        throw new AuthenticationError('Invalid refresh token');
      }

      // Get user
      const user = await UserModel.findById(session.userId);
      if (!user || user.status !== UserStatus.ACTIVE) {
        throw new AuthenticationError('User not found or inactive');
      }

      // Generate new access token
      const { accessToken } = this.generateTokens(user);
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

      // Update session with new token
      await UserSessionModel.updateActivity(session.id);

      // Cache user data
      await setCache(`user:${user.id}`, user, 3600); // 1 hour

      logger.info('Token refreshed successfully', {
        userId: user.id,
        sessionId: session.id,
        ipAddress,
      });

      return { accessToken, expiresAt };
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw error;
    }
  }

  // Logout user
  static async logout(
    sessionToken: string,
    ipAddress: string,
    userAgent?: string
  ): Promise<{ message: string }> {
    try {
      // Find session
      const session = await UserSessionModel.findByToken(sessionToken);
      if (!session) {
        return { message: 'Logout successful' }; // Don't reveal if session exists
      }

      // Revoke session
      await UserSessionModel.revoke(session.id, 'logout');

      // Remove user from cache
      await deleteCache(`user:${session.userId}`);

      // Blacklist token
      const decoded = jwt.decode(sessionToken) as any;
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await setCache(`blacklist:${sessionToken}`, true, ttl);
        }
      }

      // Log logout activity
      await UserActivityModel.create({
        userId: session.userId,
        activityType: UserActivityType.LOGOUT,
        description: 'User logged out successfully',
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.LOW,
      });

      logger.info('User logged out successfully', {
        userId: session.userId,
        sessionId: session.id,
        ipAddress,
      });

      return { message: 'Logout successful' };
    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }

  // Logout from all devices
  static async logoutAll(
    userId: string,
    ipAddress: string,
    userAgent?: string
  ): Promise<{ message: string }> {
    try {
      // Revoke all sessions for user
      const revokedCount = await UserSessionModel.revokeAllForUser(userId, 'logout_all');

      // Remove user from cache
      await deleteCache(`user:${userId}`);

      // Log logout all activity
      await UserActivityModel.create({
        userId,
        activityType: UserActivityType.LOGOUT_ALL,
        description: `User logged out from all devices (${revokedCount} sessions)`,
        ipAddress,
        userAgent,
        riskLevel: RiskLevel.LOW,
        metadata: { revokedSessions: revokedCount },
      });

      logger.info('User logged out from all devices', {
        userId,
        revokedSessions: revokedCount,
        ipAddress,
      });

      return { message: `Logged out from all devices (${revokedCount} sessions)` };
    } catch (error) {
      logger.error('Logout all failed:', error);
      throw error;
    }
  }

  // Verify email
  static async verifyEmail(token: string): Promise<{ user: User; message: string }> {
    try {
      // Find user by verification token
      const user = await UserModel.findByEmail(''); // We need to modify this to find by token
      // TODO: Add method to find user by verification token
      
      if (!user) {
        throw new ValidationError('Invalid or expired verification token');
      }

      // Verify email
      await UserModel.verifyEmail(user.id);

      // Remove user from cache to force refresh
      await deleteCache(`user:${user.id}`);

      logger.info('Email verified successfully', { userId: user.id, email: user.email });

      return {
        user: { ...user, emailVerified: true, status: UserStatus.ACTIVE },
        message: 'Email verified successfully',
      };
    } catch (error) {
      logger.error('Email verification failed:', error);
      throw error;
    }
  }

  // Generate JWT tokens
  private static generateTokens(user: User): { accessToken: string; refreshToken: string } {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
    };

    const accessToken = jwt.sign(payload, config.jwtSecret, {
      expiresIn: this.TOKEN_EXPIRY,
      issuer: 'marcat-user-service',
      audience: 'marcat-api',
    });

    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      config.jwtSecret,
      {
        expiresIn: this.REFRESH_TOKEN_EXPIRY,
        issuer: 'marcat-user-service',
        audience: 'marcat-api',
      }
    );

    return { accessToken, refreshToken };
  }

  // Validate token
  static async validateToken(token: string): Promise<User | null> {
    try {
      // Check if token is blacklisted
      const isBlacklisted = await getCache(`blacklist:${token}`);
      if (isBlacklisted) {
        return null;
      }

      // Verify JWT
      const decoded = jwt.verify(token, config.jwtSecret) as any;
      
      // Check if user exists and is active
      let user = await getCache(`user:${decoded.userId}`);
      if (!user) {
        user = await UserModel.findById(decoded.userId);
        if (user) {
          await setCache(`user:${decoded.userId}`, user, 3600); // 1 hour
        }
      }

      if (!user || user.status !== UserStatus.ACTIVE) {
        return null;
      }

      return user;
    } catch (error) {
      logger.debug('Token validation failed:', error);
      return null;
    }
  }
}
