{"name": "marcat-platform", "version": "1.0.0", "description": "Multi-store e-commerce platform specializing in men's clothing", "private": true, "workspaces": ["services/*", "shared/*"], "scripts": {"dev": "docker-compose up -d postgres redis && npm run dev:services", "dev:services": "concurrently \"npm run dev:gateway\" \"npm run dev:user\" \"npm run dev:store\" \"npm run dev:product\" \"npm run dev:order\" \"npm run dev:payment\" \"npm run dev:inventory\" \"npm run dev:notification\" \"npm run dev:analytics\" \"npm run dev:review\" \"npm run dev:support\"", "dev:gateway": "cd services/api-gateway && npm run dev", "dev:user": "cd services/user-service && npm run dev", "dev:store": "cd services/store-service && npm run dev", "dev:product": "cd services/product-service && npm run dev", "dev:order": "cd services/order-service && npm run dev", "dev:payment": "cd services/payment-service && npm run dev", "dev:inventory": "cd services/inventory-service && npm run dev", "dev:notification": "cd services/notification-service && npm run dev", "dev:analytics": "cd services/analytics-service && npm run dev", "dev:review": "cd services/review-service && npm run dev", "dev:support": "cd services/support-service && npm run dev", "build": "npm run build:services", "build:services": "npm run build:shared && npm run build:gateway && npm run build:user && npm run build:store && npm run build:product && npm run build:order && npm run build:payment && npm run build:inventory && npm run build:notification && npm run build:analytics && npm run build:review && npm run build:support", "build:shared": "cd shared/types && npm run build && cd ../utils && npm run build && cd ../constants && npm run build", "build:gateway": "cd services/api-gateway && npm run build", "build:user": "cd services/user-service && npm run build", "build:store": "cd services/store-service && npm run build", "build:product": "cd services/product-service && npm run build", "build:order": "cd services/order-service && npm run build", "build:payment": "cd services/payment-service && npm run build", "build:inventory": "cd services/inventory-service && npm run build", "build:notification": "cd services/notification-service && npm run build", "build:analytics": "cd services/analytics-service && npm run build", "build:review": "cd services/review-service && npm run build", "build:support": "cd services/support-service && npm run build", "test": "npm run test:services", "test:services": "npm run test:gateway && npm run test:user && npm run test:store && npm run test:product && npm run test:order && npm run test:payment && npm run test:inventory && npm run test:notification && npm run test:analytics && npm run test:review && npm run test:support", "test:gateway": "cd services/api-gateway && npm test", "test:user": "cd services/user-service && npm test", "test:store": "cd services/store-service && npm test", "test:product": "cd services/product-service && npm test", "test:order": "cd services/order-service && npm test", "test:payment": "cd services/payment-service && npm test", "test:inventory": "cd services/inventory-service && npm test", "test:notification": "cd services/notification-service && npm test", "test:analytics": "cd services/analytics-service && npm test", "test:review": "cd services/review-service && npm test", "test:support": "cd services/support-service && npm test", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "k8s:deploy": "kubectl apply -f infrastructure/kubernetes/", "k8s:delete": "kubectl delete -f infrastructure/kubernetes/", "migrate:up": "npm run migrate:user && npm run migrate:store && npm run migrate:product && npm run migrate:order && npm run migrate:payment && npm run migrate:inventory && npm run migrate:analytics && npm run migrate:review && npm run migrate:support", "migrate:user": "cd services/user-service && npm run migrate:up", "migrate:store": "cd services/store-service && npm run migrate:up", "migrate:product": "cd services/product-service && npm run migrate:up", "migrate:order": "cd services/order-service && npm run migrate:up", "migrate:payment": "cd services/payment-service && npm run migrate:up", "migrate:inventory": "cd services/inventory-service && npm run migrate:up", "migrate:analytics": "cd services/analytics-service && npm run migrate:up", "migrate:review": "cd services/review-service && npm run migrate:up", "migrate:support": "cd services/support-service && npm run migrate:up"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/marcat-platform.git"}, "keywords": ["e-commerce", "marketplace", "microservices", "men's clothing", "multi-store", "nodejs", "typescript", "flutter"], "author": "Marcat Team", "license": "MIT"}